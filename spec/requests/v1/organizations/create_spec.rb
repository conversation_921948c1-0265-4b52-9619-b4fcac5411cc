# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Organizations#create', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.first
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: -4
    )
  end

  let(:valid_params) do
    {
      name: 'Test',
      logo_url: 'https://www.google.com',
      monthly_credits_refresh: 1,
      max_members: 1,
      starting_credits: 1,
      refresh_date: 1,
      designated_owner_email: '<EMAIL>',
      code: 'Test'
    }
  end

  def create(params, which_user = admin)
    post '/v1/organizations', params, as_user(which_user)
  end

  describe 'POST create organization' do
    context 'when invalid params' do
      let(:invalid_params) do
        valid_params.except(:name)
      end

      it 'return error' do
        create(invalid_params, admin)
        expect_response(:unprocessable_entity)
      end
    end

    context 'when code already exists' do
      let(:invalid_params) do
        valid_params.merge(code: organization.code)
      end

      it 'return error' do
        create(invalid_params, admin)
        expect_error_response(:unprocessable_entity, 'Organization code already exists')
      end
    end

    context 'when role present' do
      it 'create organization and create membership for existing user' do
        valid_params[:role] = -1
        valid_params[:designated_owner_email] = member.email

        membership_members = Membership.where(user_id: member.id)
        expect(membership_members.count).to eq 0

        create(valid_params, admin)
        expect_response(:created)

        expect(membership_members.count).to eq 1

        membership = membership_members.first
        expect(membership.role).to eq Membership.role_mappings['partner_admin']
      end
    end

    context 'when user is partner admin' do
      before { membership_admin.update!(role: -1) }
      it 'create organization and create invitation' do
        valid_params[:designated_owner_email] = '<EMAIL>'
        create(valid_params, admin)
        expect_response(:created)

        user_invitation = UserInvitation.last
        expect(user_invitation.email).to eq '<EMAIL>'
        expect(user_invitation.invitation_status).to eq 'invited'
        expect(user_invitation.organization_id).to eq response_data['id'].to_i
        expect(user_invitation.role).to eq Membership.role_mappings['owner']

        organization = Organization.find(response_data['id'])
        expect(organization.created_by_id).to eq admin.id.to_s
      end

      it 'create organization and create membership on existing user' do
        create(valid_params, admin)
        expect_response(:created)

        membership = Membership.find_by(user_id: member.id)
        expect(membership).to be_present
        expect(membership.role).to eq Membership.role_mappings['owner']
      end

      it 'return error when create partner admin' do
        valid_params[:role] = -1
        create(valid_params, admin)
        expect_response(:forbidden)
      end
    end

    it 'create organization with code' do
      create(valid_params, admin)
      expect_response(:created)

      expect(response_data['code']).to eq 'test'
    end

    it 'create organization with parameterized name code' do
      params = valid_params.merge(name: 'Test 1').except(:code)
      create(params, admin)
      expect_response(:created)

      expect(response_data['code']).to eq 'test-1'

      params = valid_params.merge(name: 'Test 2', code: '')
      create(params, admin)
      expect_response(:created)

      expect(response_data['code']).to eq 'test-2'
    end

    it 'create organization and create invitation for new user' do
      valid_params[:designated_owner_email] = '<EMAIL>'
      create(valid_params, admin)
      expect_response(:created)

      user_invitation = UserInvitation.last
      expect(user_invitation.email).to eq '<EMAIL>'
      expect(user_invitation.invitation_status).to eq 'invited'
      expect(user_invitation.organization_id).to eq response_data['id'].to_i
      expect(user_invitation.role).to eq Membership.role_mappings['owner']
    end

    it 'create organization and create membership for existing user' do
      valid_params[:designated_owner_email] = member.email

      membership_members = Membership.where(user_id: member.id)
      expect(membership_members.count).to eq 0

      create(valid_params, admin)
      expect_response(:created)

      expect(membership_members.count).to eq 1
      membership = membership_members.first
      expect(membership.role).to eq Membership.role_mappings['owner']
    end

    it 'create organization with created_by_id' do
      create(valid_params, admin)
      expect_response(:created)

      expect(response_data['created_by_id']).to eq admin.id.to_s

      organization = Organization.find(response_data['id'])
      expect(organization.created_by_id).to eq admin.id.to_s
    end

    it 'create organization with is_partner false' do
      create(valid_params, admin)
      expect_response(:created)

      expect(response_data['is_partner']).to eq false
    end
  end
end
