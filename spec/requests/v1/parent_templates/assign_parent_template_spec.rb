# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'V1::ParentTemplates', type: :request do
  let!(:test_user) { create(:user, display_name: 'Test') }
  let!(:organization) { create(:organization, name: 'Test') }
  let!(:membership_test_user) { create(:membership, user: test_user, organization: organization) }

  # Platform admin users
  let!(:platform_admin) { create(:user, display_name: 'Platform Admin') }
  let!(:membership_platform_admin) { create(:membership, :admin, user: platform_admin, organization: organization) }

  # Partner admin user
  let!(:partner_admin) { create(:user) }
  let!(:partner_org) { create(:organization) }
  let!(:membership_partner_admin) do
    create(:membership, :partner_admin, user: partner_admin, organization: partner_org)
  end

  let!(:template_without_parent) { create(:model_template, organization:, in_bank: true) }

  let(:valid_params) do
    {
      organization_id: partner_org.id
    }
  end

  def assign(id, params, which_user = admin)
    post "/v1/parent_templates/#{id}/assign", params, as_user(which_user)
  end

  describe 'POST assign' do
    it 'assigns parent template successfully' do
      assign(template_without_parent.id, valid_params, platform_admin)
      expect_response(:ok)

      # Verify the parent was assigned
      assigned_template = PartnerAssignedModelTemplate.find_by(model_template_id: template_without_parent.id)
      expect(assigned_template).to be_present
      expect(assigned_template.model_template_id).to eq(template_without_parent.id)
      expect(assigned_template.organization_id).to eq(valid_params[:organization_id])
    end

    it 'return error when org do not had any partner admin' do
      membership_partner_admin.discard!
      assign(template_without_parent.id, valid_params, platform_admin)
      expect_response(:forbidden)
    end

    it 'return error when not had any authentication' do
      post "/v1/parent_templates/#{template_without_parent.id}/assign", valid_params
      expect_response(:unauthorized)
    end
  end
end
