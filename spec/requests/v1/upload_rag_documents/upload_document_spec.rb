# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::UploadRagDocuments#create', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }

  let(:valid_params) do
    {
      url: 'https://example.com/document.pdf',
      document_type: 'knowledge_file'
    }
  end

  let(:mock_rag_service) { double('External::RagService') }
  let(:mock_upload_response) { { 'id' => 'doc_123456' } }

  def upload_document(params, which_user = user)
    post '/v1/upload_rag_documents', params, as_user(which_user)
  end

  before do
    allow(External::RagService).to receive(:new).and_return(mock_rag_service)
    allow(mock_rag_service).to receive(:upload_document).and_return(mock_upload_response)
  end

  describe 'POST /v1/upload_rag_documents' do
    context 'with valid parameters' do
      it 'uploads a document successfully' do
        upload_document(valid_params)
        expect_response(:created)

        expect(response_data['id']).to be_present
        expect(response_data['document_id']).to eq('doc_123456')
      end

      it 'creates an OpenaiFile record' do
        expect { upload_document(valid_params) }.to change(OpenaiFile, :count).by(1)

        openai_file = OpenaiFile.last
        expect(openai_file.object_class).to eq('RagDocument')
        expect(openai_file.object_class_column).to eq('knowledge_file')
        expect(openai_file.openai_file_id).to eq('doc_123456')
        expect(openai_file.object_id).to be_present
      end

      it 'calls the RagService with correct parameters' do
        expect(External::RagService).to receive(:new).with(organization.id)
        expect(mock_rag_service).to receive(:upload_document).with(
          hash_including(url: 'https://example.com/document.pdf')
        )

        upload_document(valid_params)
      end

      context 'with different document types' do
        %w[message template_variable template_input reference_output knowledge_file].each do |doc_type|
          it "accepts document_type: #{doc_type}" do
            params = valid_params.merge(document_type: doc_type)
            upload_document(params)
            expect_response(:created)

            openai_file = OpenaiFile.last
            expect(openai_file.object_class_column).to eq(doc_type)
          end
        end
      end
    end

    context 'with invalid parameters' do
      it 'returns validation error when url is missing' do
        params = valid_params.except(:url)
        upload_document(params)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation error when document_type is missing' do
        params = valid_params.except(:document_type)
        upload_document(params)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation error for invalid document_type' do
        params = valid_params.merge(document_type: 'invalid_type')
        upload_document(params)
        expect_error_response(:unprocessable_entity)
      end

      it 'accepts empty url (validation allows it)' do
        params = valid_params.merge(url: '')
        upload_document(params)
        expect_response(:created)
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        post '/v1/upload_rag_documents', valid_params
        expect_error_response(:unauthorized)
      end
    end
  end
end
