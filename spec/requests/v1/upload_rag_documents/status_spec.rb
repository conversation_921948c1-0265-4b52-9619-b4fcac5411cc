# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::UploadRagDocuments#show', type: :request do
  let!(:user) { create(:user) }
  let!(:organization) { create(:organization) }
  let!(:membership) { create(:membership, user: user, organization: organization) }

  let(:document_id) { 'doc_123456' }
  let(:mock_rag_service) { double('External::RagService') }
  let(:mock_status_response) do
    {
      'id' => document_id,
      'status' => 'processing',
      'total_pages' => 10,
      'filename' => 'document.pdf',
      'metadata' => 'Some metadata'
    }
  end

  def get_document_status(doc_id, which_user = user)
    get "/v1/upload_rag_documents/#{doc_id}", {}, as_user(which_user)
  end

  before do
    allow(External::RagService).to receive(:new).and_return(mock_rag_service)
    allow(mock_rag_service).to receive(:status).and_return(mock_status_response)
  end

  describe 'GET /v1/upload_rag_documents/:id' do
    context 'with valid document id' do
      it 'returns document status successfully' do
        get_document_status(document_id)
        expect_response(:ok)

        expect(response_data['id']).to eq(document_id)
        expect(response_data['status']).to eq('processing')
      end

      it 'calls the RagService with correct parameters' do
        expect(External::RagService).to receive(:new).with(organization.id)
        expect(mock_rag_service).to receive(:status).with(document_id)

        get_document_status(document_id)
      end

      context 'with different status values' do
        %w[pending processing ready failed].each do |status_value|
          it "handles status: #{status_value}" do
            allow(mock_rag_service).to receive(:status).and_return(
              mock_status_response.merge('status' => status_value)
            )

            get_document_status(document_id)
            expect_response(:ok)
            expect(response_data['status']).to eq(status_value)
          end
        end
      end

      it 'returns status fields from the service' do
        get_document_status(document_id)
        expect_response(:ok)

        # The service creates an OpenStruct with id and status fields
        expect(response_data['id']).to eq(document_id)
        expect(response_data['status']).to eq('processing')
      end
    end

    context 'when user is not authenticated' do
      it 'returns unauthorized error' do
        get "/v1/upload_rag_documents/#{document_id}"
        expect_error_response(:unauthorized)
      end
    end

    context 'with different document IDs' do
      it 'handles numeric document IDs' do
        numeric_id = '123456'
        get_document_status(numeric_id)
        expect_response(:ok)
        expect(response_data['id']).to eq(numeric_id)
      end

      it 'handles alphanumeric document IDs' do
        alpha_id = 'doc_abc123xyz'
        get_document_status(alpha_id)
        expect_response(:ok)
        expect(response_data['id']).to eq(alpha_id)
      end
    end
  end
end
