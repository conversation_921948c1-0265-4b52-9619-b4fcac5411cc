# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::UserManagements#accept_invitation', type: :request do
  let!(:admin) { create(:user) }
  let!(:organization) { create(:organization) }

  let!(:organization_plan_thresholds) do
    create(:organizations_plans_threshold, organization:)
  end

  let!(:membership_admin) { create(:membership, :admin, user: admin, organization:) }
  let!(:user_invitation) do
    create(:user_invitation, email: '<EMAIL>', organization:, invitation_status: 'invited')
  end

  let(:valid_params) do
    {
      invite_code: user_invitation.invitation_code,
      name: 'New User',
      password: 'password123'
    }
  end

  def accept_invitation(params, user)
    post '/v1/user_managements/accept_invitation', params, as_user(user)
  end

  describe 'POST /v1/user_managements/accept_invitation' do
    context 'when request invalid' do
      it 'returns 422 with error message' do
        accept_invitation({}, admin)
        expect_response(:unprocessable_entity)
      end
    end

    context 'when request valid' do
      it 'returns 200 with user data' do
        accept_invitation(valid_params, admin)
        expect_response(:created)
      end
    end

    context 'when user already accepted' do
      it 'returns 403 with error message' do
        user_invitation.update!(invitation_status: 'confirmed')
        accept_invitation(valid_params, admin)
        expect_response(:forbidden)
      end
    end

    context 'when user already exist' do
      it 'returns 403 with error message' do
        create(:user, email: '<EMAIL>')
        accept_invitation(valid_params, admin)
        expect_response(:forbidden)
      end
    end

    context 'when user is partner admin' do
      it 'returns 200 with user data' do
        user_invitation.update!(role: -1)
        accept_invitation(valid_params, admin)
        expect_response(:created)

        organization.reload
        expect(organization.is_partner).to be_truthy
      end
    end
  end
end
