require 'rails_helper'

RSpec.describe V1::ModelTemplatesController, type: :request do
  let(:test_user) { create(:user) }
  let(:test_user2) { create(:user) }
  let(:organization) { create(:organization, name: 'Test') }
  let(:workspace) do
    Workspace.create!(
      organization_id: organization.id,
      name: 'Test'
    )
  end
  let(:membership_test_user) { create(:membership, user: test_user, organization: organization) }
  let(:workspace_membership) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user.id,
      workspace_id: workspace.id
    )
  end
  let(:membership_test_user2) do
    Membership.create!(
      user_id: test_user2.id,
      organization_id: organization.id
    )
  end
  let(:workspace_membership2) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user2.id,
      workspace_id: workspace.id
    )
  end

  let(:test_user_org2) do
    User.create!(
      display_name: 'Test Org2',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization2) { create(:organization, name: 'Test2') }
  let(:workspace2) do
    Workspace.create!(
      organization_id: organization2.id,
      name: 'Test2'
    )
  end
  let(:membership_test_user_org2) do
    Membership.create!(
      user_id: test_user_org2.id,
      organization_id: organization2.id
    )
  end
  let(:workspace_membership_org2) do
    WorkspacesMembership.create!(
      membership_id: membership_test_user_org2.id,
      workspace_id: workspace2.id
    )
  end

  # Platform admin users
  let(:platform_admin) do
    User.create!(
      display_name: 'Platform Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:membership_platform_admin) do
    Membership.create!(
      user_id: platform_admin.id,
      organization_id: organization.id,
      role: Membership.role_mappings['admin']
    )
  end

  # Partner admin user
  let(:partner_admin) do
    User.create!(
      display_name: 'Partner Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization_created_by_partner) do
    Organization.create!(name: 'Partner Org', created_by_id: partner_admin.id)
  end
  let(:membership_partner_admin) do
    Membership.create!(
      user_id: partner_admin.id,
      organization_id: organization_created_by_partner.id,
      role: Membership.role_mappings['partner_admin']
    )
  end

  let(:model) do
    Model.create!(
      name: 'ChatGPT gpt-4o',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      openai_assistant_id: 'asst_abc098'
    )
  end
  let(:model_template) do
    ModelTemplate.create!(
      name: 'Test Template',
      description: 'Test Desc',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization.id,
      user: test_user,
      draft: false
    )
  end
  let(:model_template2) do
    ModelTemplate.create!(
      name: 'Test Template2',
      description: 'Test Desc2',
      max_tokens: 100,
      temperature: 1.0,
      model: 'openai/gpt-4o',
      instruction: 'Chat GPT',
      prompt: 'test',
      placeholder: 'reference',
      organization_id: organization2.id,
      user: test_user_org2,
      draft: false
    )
  end

  def index_model_templates(params, user)
    get '/v1/model_templates', params, as_user(user)
  end

  # need to comment before_create line in user model first
  describe 'GET list model templates' do
    before do
      # init data
      organization
      test_user
      test_user2
      workspace
      membership_test_user
      membership_test_user2
      workspace_membership
      workspace_membership2
      model
      model_template
      model_template2

      test_user_org2
      membership_test_user_org2
      workspace_membership_org2

      # Platform admin
      platform_admin
      membership_platform_admin

      # Partner admin
      partner_admin
      organization_created_by_partner
      membership_partner_admin
    end

    it 'return list model templates from same organization' do
      expect(ModelTemplate.all.count).to eq 2

      index_model_templates({}, test_user)
      expect_response(:ok)

      expect(response_data.size).to eq 1

      # Verify new fields are included
      expect(response_data.first).to have_key('organization_name')
      expect(response_data.first).to have_key('parent_name')
      expect(response_data.first['organization_name']).to eq 'Test'
      expect(response_data.first['parent_name']).to be_nil
      expect(response_data.first['created_at']).to be_present
    end

    context 'with model ratings present' do
      before do
        ModelRating.create!(
          rating: 5,
          user_id: test_user.id,
          model_template_id: model_template.id,
          comment: nil
        )

        ModelRating.create!(
          rating: 4,
          user_id: test_user2.id,
          model_template_id: model_template.id,
          comment: 'comment 2'
        )

        ModelRating.create!(
          rating: 3,
          user_id: test_user_org2.id,
          model_template_id: model_template2.id,
          comment: 'comment 3'
        )
      end

      it 'return list model templates with number_of_comments' do
        index_model_templates({}, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1

        expect(response_data.first).to have_key('number_of_comments')
        expect(response_data.first['number_of_comments']).to eq 2
      end

      it 'return model template with assigned rating if any present' do
        model_template.update!(rating: 5)

        index_model_templates({}, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1

        expect(response_data.first).to have_key('rating')
        expect(response_data.first['rating']).to eq 5
      end
    end

    context 'with assigned organizations' do
      let(:another_organization) { create(:organization) }

      let!(:bank_template) do
        ModelTemplate.create!(
          name: 'Bank Template',
          description: 'Test',
          prompt: 'Bank template prompt',
          model: 'gpt-3.5-turbo',
          organization_id: organization.id,
          in_bank: true,
          bank_notes: 'Added to bank for testing',
          user: platform_admin
        )
      end

      before do
        # Assign the bank template to another organization
        PartnerAssignedModelTemplate.create!(
          model_template_id: bank_template.id,
          organization_id: another_organization.id
        )
      end

      it 'returns templates with assigned organizations information' do
        index_model_templates({}, platform_admin)
        expect_response(:ok)

        bank_template_response = response_data.find { |t| t['id'] == bank_template.id }
        expect(bank_template_response).to be_present
        expect(bank_template_response['assigned_organizations']).to be_present
        expect(bank_template_response['assigned_organizations'].size).to eq 1
        expect(bank_template_response['assigned_organizations'].first['id']).to eq another_organization.id
      end

      it 'returns null for assigned_organizations when no organizations are assigned' do
        # Create a template without any assigned organizations
        template = ModelTemplate.create!(
          name: 'Unassigned Template',
          description: 'Template with no assigned organizations',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          draft: false
        )

        index_model_templates({}, test_user)
        expect_response(:ok)

        template_response = response_data.find { |t| t['id'] == template.id }
        expect(template_response).to be_present
        expect(template_response['assigned_organizations']).to be_nil
      end
    end

    context 'with parent-child relationships' do
      let(:parent_template) do
        ModelTemplate.create!(
          name: 'Parent Template',
          description: 'Parent Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: nil,
          draft: false
        )
      end

      let(:child_template) do
        ModelTemplate.create!(
          name: 'Child Template',
          description: 'Child Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: parent_template.id,
          draft: false
        )
      end

      before do
        parent_template
        child_template
      end

      it 'return child template with parent_name' do
        index_model_templates({}, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 3 # parent + child + original template

        child_response = response_data.find { |t| t['name'] == 'Child Template' }
        expect(child_response).to be_present
        expect(child_response['parent_name']).to eq 'Parent Template'
        expect(child_response['organization_name']).to eq 'Test'
      end

      it 'get children of a specific parent' do
        index_model_templates({ parent_id: parent_template.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Child Template'
        expect(response_data.first['parent_id']).to eq parent_template.id
      end

      it 'get parent of a specific child' do
        index_model_templates({ child_id: child_template.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Parent Template'
        expect(response_data.first['id']).to eq parent_template.id
      end

      it 'return child template with duplicated_from information' do
        index_model_templates({}, test_user)
        expect_response(:ok)

        child_response = response_data.find { |t| t['name'] == 'Child Template' }
        expect(child_response).to be_present
        expect(child_response['duplicated_from']).to be_present
        expect(child_response['duplicated_from']['id']).to eq organization.id
        expect(child_response['duplicated_from']['name']).to eq organization.name
        expect(child_response['duplicated_from']['is_partner']).to eq organization.is_partner
      end

      it 'return parent template with duplicated_to information' do
        index_model_templates({}, test_user)
        expect_response(:ok)

        parent_response = response_data.find { |t| t['name'] == 'Parent Template' }
        expect(parent_response).to be_present
        expect(parent_response['duplicated_to']).to be_present
        expect(parent_response['duplicated_to']).to be_an(Array)
        expect(parent_response['duplicated_to'].first['id']).to eq organization.id
        expect(parent_response['duplicated_to'].first['name']).to eq organization.name
        expect(parent_response['duplicated_to'].first['is_partner']).to eq organization.is_partner
        expect(parent_response['duplicated_to'].first['times_duplicated']).to eq 1
      end

      it 'return empty result when child has no parent' do
        orphan_child = ModelTemplate.create!(
          name: 'Orphan Child',
          description: 'Child without parent',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: nil,
          draft: false
        )

        index_model_templates({ child_id: orphan_child.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 0
      end
    end

    context 'with status filtering' do
      let(:verified_template) do
        ModelTemplate.create!(
          name: 'Verified Template',
          description: 'Verified Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          verified: true,
          draft: false
        )
      end

      let(:draft_template) do
        ModelTemplate.create!(
          name: 'Draft Template',
          description: 'Draft Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          verified: false,
          draft: true
        )
      end

      let(:expert_template) do
        ModelTemplate.create!(
          name: 'Expert Template',
          description: 'Expert Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          template_type: 'expert',
          draft: false
        )
      end

      before do
        verified_template
        draft_template
        expert_template
      end

      it 'filter by verified status' do
        index_model_templates({ verified: true }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Verified Template'
        expect(response_data.first['verified']).to be true
      end

      it 'filter by draft status' do
        index_model_templates({ draft: true }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Draft Template'
        expect(response_data.first['draft']).to be true
      end

      it 'filter by template type' do
        index_model_templates({ template_type: 'expert' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Expert Template'
        expect(response_data.first['template_type']).to eq 'expert'
      end

      it 'filter by organization prompt status' do
        ModelTemplate.create!(
          name: 'Org Prompt Template',
          description: 'Org Prompt Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          organization_prompt: true
        )

        index_model_templates({ organization_prompt: true }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Org Prompt Template'
        expect(response_data.first['organization_prompt']).to be true
      end

      it 'combine multiple status filters' do
        index_model_templates({ verified: false, draft: true }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Draft Template'
        expect(response_data.first['verified']).to be false
        expect(response_data.first['draft']).to be true
      end
    end

    context 'with template category filtering' do
      let(:template_category) do
        TemplateCategory.create!(
          name: 'Test Category',
          organization: organization
        )
      end

      let(:categorized_template) do
        ModelTemplate.create!(
          name: 'Categorized Template',
          description: 'Categorized Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          template_category: template_category
        )
      end

      before do
        template_category
        categorized_template
      end

      it 'filter by template category' do
        index_model_templates({ template_category_id: template_category.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Categorized Template'
        expect(response_data.first['template_category']['name']).to eq 'Test Category'
      end
    end

    context 'with search functionality' do
      let(:searchable_template) do
        ModelTemplate.create!(
          name: 'Marketing Assistant',
          description: 'A helpful marketing assistant for creating campaigns',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are a marketing expert',
          prompt: 'Create marketing content for the given product',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          draft: false
        )
      end

      let(:another_template) do
        ModelTemplate.create!(
          name: 'Sales Helper',
          description: 'A sales assistant for closing deals',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are a sales expert',
          prompt: 'Help with sales strategies',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          draft: false
        )
      end

      before do
        searchable_template
        another_template
      end

      it 'search by name' do
        index_model_templates({ search: 'Marketing' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Marketing Assistant'
      end

      it 'search is case insensitive' do
        index_model_templates({ search: 'MARKETING' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Marketing Assistant'
      end

      it 'search with partial match' do
        index_model_templates({ search: 'market' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Marketing Assistant'
      end

      it 'search returns no results for non-matching term' do
        index_model_templates({ search: 'nonexistent' }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 0
      end

      it 'combine search with other filters' do
        # Create a verified marketing template
        ModelTemplate.create!(
          name: 'Verified Marketing',
          description: 'A verified marketing template',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Marketing expert',
          prompt: 'Marketing content',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          verified: true
        )

        index_model_templates({ search: 'marketing', verified: true }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Verified Marketing'
        expect(response_data.first['verified']).to be true
      end
    end

    context 'with bank operations' do
      let(:bank_template) do
        ModelTemplate.create!(
          name: 'Bank Template',
          description: 'A template in the bank',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are a bank template',
          prompt: 'Bank template prompt',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          in_bank: true,
          verified: true,
          bank_added_by: platform_admin.id,
          bank_added_at: Time.current,
          bank_notes: 'High performing template'
        )
      end

      let(:non_bank_template) do
        ModelTemplate.create!(
          name: 'Non Bank Template',
          description: 'A template not in the bank',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are a regular template',
          prompt: 'Regular template prompt',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          in_bank: false
        )
      end

      let(:partner_org_template) do
        ModelTemplate.create!(
          name: 'Partner Org Template',
          description: 'A template from partner organization',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are a partner template',
          prompt: 'Partner template prompt',
          placeholder: 'reference',
          organization_id: organization_created_by_partner.id,
          user: partner_admin,
          in_bank: true,
          verified: true,
          bank_added_by: partner_admin.id,
          bank_added_at: Time.current,
          bank_notes: 'Partner high performing template'
        )
      end

      before do
        bank_template
        non_bank_template
        partner_org_template
      end

      it 'filter by bank status - show only bank templates' do
        index_model_templates({ in_bank: true }, platform_admin)
        expect_response(:ok)

        expect(response_data.size).to eq 2
        bank_template_names = response_data.map { |t| t['name'] }
        expect(bank_template_names).to include('Bank Template', 'Partner Org Template')

        # Verify bank fields are present
        bank_response = response_data.find { |t| t['name'] == 'Bank Template' }
        expect(bank_response['in_bank']).to be true
        expect(bank_response['verified']).to be true
        expect(bank_response['bank_added_by']).to be_present
        expect(bank_response['bank_added_at']).to be_present
        expect(bank_response['bank_notes']).to eq 'High performing template'
      end

      it 'filter by bank status - show only non-bank templates' do
        index_model_templates({ in_bank: false }, test_user)
        expect_response(:ok)

        # Should only show templates from user's organization that are not in bank
        non_bank_templates = response_data.select { |t| t['in_bank'] == false }
        expect(non_bank_templates.size).to eq 2
        expect(non_bank_templates.first['name']).to eq 'Non Bank Template'
        expect(non_bank_templates.first['in_bank']).to be false
      end

      it 'partner admin can only see templates from their created organizations and their own organization' do
        index_model_templates({ in_bank: true }, partner_admin)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Partner Org Template'
        expect(response_data.first['organization_id']).to eq organization_created_by_partner.id
      end

      it 'combine bank filter with other filters' do
        index_model_templates({ in_bank: true, search: 'Bank' }, platform_admin)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Bank Template'
        expect(response_data.first['in_bank']).to be true
      end

      it 'create template directly in bank successfully' do
        template_params = {
          name: 'Direct Bank Template',
          description: 'Template created directly in bank',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are a direct bank template',
          prompt: 'Direct bank template prompt',
          placeholder: 'reference',
          in_bank: true,
          bank_notes: 'Created directly in bank'
        }

        post '/v1/model_templates', template_params, as_user(platform_admin)
        expect_response(:created)

        expect(response_data['in_bank']).to be true
        expect(response_data['verified']).to be true
      end

      it 'fail to create template in bank without proper authorization' do
        template_params = {
          name: 'Unauthorized Bank Template',
          description: 'Template created without authorization',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are an unauthorized bank template',
          prompt: 'Unauthorized bank template prompt',
          placeholder: 'reference',
          in_bank: true
        }

        post '/v1/model_templates', template_params, as_user(test_user)
        expect_response(:forbidden)
      end

      it 'partner admin can create template in bank from their organization' do
        template_params = {
          name: 'Partner Bank Template',
          description: 'Template created by partner admin in bank',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are a partner bank template',
          prompt: 'Partner bank template prompt',
          placeholder: 'reference',
          in_bank: true,
          bank_notes: 'Partner created this in bank'
        }

        post '/v1/model_templates', template_params, as_user(partner_admin)
        expect_response(:created)

        expect(response_data['in_bank']).to be true
        expect(response_data['verified']).to be true
      end

      it 'partner admin cannot create template in bank from other organization' do
        template_params = {
          name: 'Other Org Bank Template',
          description: 'Template created by partner admin from other org',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'You are an other org bank template',
          prompt: 'Other org bank template prompt',
          placeholder: 'reference',
          in_bank: true,
          organization_id: organization.id # Different organization
        }

        post '/v1/model_templates', template_params, as_user(partner_admin)
        expect_response(:forbidden)
      end
    end

    context 'with duplication filters' do
      let(:parent_template) do
        ModelTemplate.create!(
          name: 'Parent Template',
          description: 'Parent Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: nil,
          draft: false
        )
      end

      let(:child_template) do
        ModelTemplate.create!(
          name: 'Child Template',
          description: 'Child Desc',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: parent_template.id,
          draft: false
        )
      end

      before do
        parent_template
        child_template
      end

      it 'filter by duplicated_from organization' do
        index_model_templates({ duplicated_from: organization.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Child Template'
        expect(response_data.first['parent_id']).to eq parent_template.id
      end

      it 'filter by duplicated_to organization' do
        index_model_templates({ duplicated_to: organization.id }, test_user)
        expect_response(:ok)

        expect(response_data.size).to eq 1
        expect(response_data.first['name']).to eq 'Parent Template'
        expect(response_data.first['id']).to eq parent_template.id
      end

      it 'return is_partner information for partner organizations' do
        # Create a partner organization
        partner_org = create(:organization, is_partner: true)

        # Create a parent template in the partner organization
        partner_parent = ModelTemplate.create!(
          name: 'Partner Parent Template',
          description: 'Parent from partner org',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: partner_org.id,
          user: test_user,
          parent_id: nil,
          draft: false
        )

        # Create a child template in the regular organization
        ModelTemplate.create!(
          name: 'Partner Child Template',
          description: 'Child from partner parent',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: partner_parent.id,
          draft: false
        )

        index_model_templates({}, test_user)
        expect_response(:ok)

        child_response = response_data.find { |t| t['name'] == 'Partner Child Template' }
        expect(child_response).to be_present
        expect(child_response['duplicated_from']).to be_present
        expect(child_response['duplicated_from']['is_partner']).to be true
        expect(child_response['duplicated_from']['id']).to eq partner_org.id
      end

      it 'return times_duplicated count for multiple duplications to same organization' do
        # Create a parent template
        parent_template = ModelTemplate.create!(
          name: 'Multi Duplicate Parent',
          description: 'Parent for multiple duplications',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: nil,
          draft: false
        )

        # Create multiple child templates in the same organization
        ModelTemplate.create!(
          name: 'Child 1',
          description: 'First child',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: parent_template.id,
          draft: false
        )

        ModelTemplate.create!(
          name: 'Child 2',
          description: 'Second child',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: organization.id,
          user: test_user,
          parent_id: parent_template.id,
          draft: false
        )

        # Create child in different organization
        other_org = create(:organization)
        ModelTemplate.create!(
          name: 'Child 3',
          description: 'Child in different org',
          max_tokens: 100,
          temperature: 1.0,
          model: 'openai/gpt-4o',
          instruction: 'Chat GPT',
          prompt: 'test',
          placeholder: 'reference',
          organization_id: other_org.id,
          user: test_user,
          parent_id: parent_template.id,
          draft: false
        )

        index_model_templates({}, test_user)
        expect_response(:ok)

        parent_response = response_data.find { |t| t['name'] == 'Multi Duplicate Parent' }
        expect(parent_response).to be_present
        expect(parent_response['duplicated_to']).to be_present
        expect(parent_response['duplicated_to'].size).to eq 2

        # Check organization with 2 duplications
        org_with_multiple = parent_response['duplicated_to'].find { |org| org['id'] == organization.id }
        expect(org_with_multiple).to be_present
        expect(org_with_multiple['times_duplicated']).to eq 2

        # Check organization with 1 duplication
        org_with_single = parent_response['duplicated_to'].find { |org| org['id'] == other_org.id }
        expect(org_with_single).to be_present
        expect(org_with_single['times_duplicated']).to eq 1
      end
    end

    # TODO: add more context: filter, with variables present, etc
  end
end
