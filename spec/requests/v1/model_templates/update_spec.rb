# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::ModelTemplate#update', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: :password
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: :password
    )
  end

  let!(:organization) { create(:organization, name: 'Main Org') }

  let!(:organization2) { create(:organization, name: 'Second Org') }

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: organization.id,
      role: 3
    )
  end

  let!(:model_template) do
    ModelTemplate.create!(
      name: 'Test Template',
      description: 'This is a test template',
      max_tokens: 1000,
      model: 'openai/gpt-4o',
      instruction: 'Test instruction',
      prompt: 'Test prompt',
      placeholder: 'Test placeholder',
      template_type: 'default',
      verified: true,
      organization_id: organization.id,
      user_id: admin.id
    )
  end

  let(:valid_params) do
    {
      name: 'Updated Template'
    }
  end

  def update(params, which_user = admin)
    put "/v1/model_templates/#{model_template.id}", params, as_user(which_user)
  end

  describe 'PUT update model template' do
    context 'when invalid params' do
      it 'returns error when name is missing' do
        update({ name: nil }, admin)
        expect_response(:unprocessable_entity)
      end
    end

    context 'when reference_output_url is provided' do
      it 'updates the model template when no previous url exists' do
        allow(External::Ragie).to receive(:new).and_return(double(create_document_from_url: { 'id' => 'doc_123' }))
        update({ reference_output_url: 'http://new-url.com' }, admin)
        expect_response(:success)

        openai_file = OpenaiFile.find_by(object_id: model_template.id)
        expect(openai_file).to be_present
        expect(openai_file.openai_file_id).to eq('doc_123')
      end

      it 'updates the model template and does not create a new OpenAI file if the URL is unchanged' do
        model_template.update!(reference_output_url: 'http://existing-url.com')

        openai_file = OpenaiFile.create!(
          object_id: model_template.id,
          object_class: 'ModelTemplate',
          object_class_column: 'reference_output_url',
          openai_file_id: 'doc_123'
        )

        update({ reference_output_url: 'http://existing-url.com' }, admin)
        expect_response(:success)
        expect(OpenaiFile.exists?(openai_file.id)).to be_truthy
      end

      it 'updates the model template and creates a new OpenAI file if the URL is changed' do
        model_template.update!(reference_output_url: 'http://existing-url.com')
        allow(External::Ragie).to receive(:new).and_return(double(delete_document: true,
                                                                  create_document_from_url: { 'id' => 'doc_786' }))
        openai_file = OpenaiFile.create!(
          object_id: model_template.id,
          object_class: 'ModelTemplate',
          object_class_column: 'reference_output_url',
          openai_file_id: 'doc_456'
        )
        update({ reference_output_url: 'http://new-url.com' }, admin)
        expect_response(:success)

        expect(openai_file.reload).to be_discarded

        new_openai_file = OpenaiFile.find_by(object_id: model_template.id)
        expect(new_openai_file).to be_present
        expect(new_openai_file.openai_file_id).to eq('doc_786')
      end

      it 'updates the model template and deletes the OpenAI file if the URL is removed' do
        model_template.update!(reference_output_url: 'http://existing-url.com')
        openai_file = OpenaiFile.create!(
          object_id: model_template.id,
          object_class: 'ModelTemplate',
          object_class_column: 'reference_output_url',
          openai_file_id: 'doc_123'
        )

        allow(External::Ragie).to receive(:new).and_return(double(delete_document: true))
        update({ reference_output_url: nil }, admin)
        expect_response(:success)

        expect(OpenaiFile.exists?(openai_file.id)).to be_falsey
      end
    end

    context 'when file_id is provided' do
      let!(:uploaded_file) do
        OpenaiFile.create!(
          object_id: SecureRandom.uuid,
          object_class: 'RagDocument',
          object_class_column: 'reference_output',
          openai_file_id: 'rag_doc_456'
        )
      end

      it 'updates the model template and associates the new file when no previous file exists' do
        update({ file_id: uploaded_file.id }, admin)
        expect_response(:success)

        uploaded_file.reload
        expect(uploaded_file.object_id).to eq(model_template.id.to_s)
        expect(uploaded_file.object_class).to eq('ModelTemplate')
        expect(uploaded_file.object_class_column).to eq('reference_output_url')
        expect(uploaded_file.openai_file_id).to eq('rag_doc_456')
      end

      it 'updates the model template and does not change file association if file_id is unchanged' do
        model_template.update!(file_id: uploaded_file.id)
        uploaded_file.update!(
          object_id: model_template.id,
          object_class: 'ModelTemplate',
          object_class_column: 'reference_output_url'
        )

        update({ file_id: uploaded_file.id }, admin)
        expect_response(:success)

        uploaded_file.reload
        expect(uploaded_file.object_id).to eq(model_template.id.to_s)
        expect(uploaded_file.object_class).to eq('ModelTemplate')
        expect(uploaded_file.object_class_column).to eq('reference_output_url')
      end

      it 'updates the model template and changes file association if file_id is changed' do
        # Create existing file association
        existing_file = OpenaiFile.create!(
          object_id: model_template.id,
          object_class: 'ModelTemplate',
          object_class_column: 'reference_output_url',
          openai_file_id: 'existing_doc_123'
        )
        model_template.update!(file_id: existing_file.id)

        update({ file_id: uploaded_file.id }, admin)
        expect_response(:success)

        # Check that existing file is discarded
        existing_file.reload
        expect(existing_file).to be_discarded

        # Check that new file is associated
        uploaded_file.reload
        expect(uploaded_file.object_id).to eq(model_template.id.to_s)
        expect(uploaded_file.object_class).to eq('ModelTemplate')
        expect(uploaded_file.object_class_column).to eq('reference_output_url')
      end

      it 'updates the model template and discards file association if file_id is removed' do
        existing_file = OpenaiFile.create!(
          object_id: model_template.id,
          object_class: 'ModelTemplate',
          object_class_column: 'reference_output_url',
          openai_file_id: 'existing_doc_123'
        )
        model_template.update!(file_id: existing_file.id)

        update({ file_id: nil }, admin)
        expect_response(:success)

        existing_file.reload
        expect(existing_file).to be_discarded
      end

      it 'raises error when file_id does not exist' do
        update({ file_id: 999_999 }, admin)
        expect_response(:not_found)
      end
    end

    it 'update instruction model to current system prompt' do
      update(valid_params, admin)
      expect_response(:success)

      models = Model.where(model_template_id: model_template.id)
      expect(models.first.instruction).to include('This agent is used to help users generate outputs for the use case of')
      expect(models.first.instruction).not_to include('<agent_caption_context>')
      expect(models.first.instruction).to include(model_template.description)
    end
  end
end
