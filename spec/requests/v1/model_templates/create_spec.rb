# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::ModelTemplate#create', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: :password
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: :password
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Main Org')
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: organization.id,
      role: 3
    )
  end

  let(:valid_params) do
    {
      name: 'Test Template',
      description: 'This is a test template',
      max_tokens: 1000,
      model: 'openai/gpt-4o',
      instruction: 'Test instruction',
      prompt: 'Test prompt',
      placeholder: 'Test placeholder',
      template_type: 'default',
      verified: true
    }
  end

  def create(params, which_user = admin)
    post '/v1/model_templates', params, as_user(which_user)
  end

  describe 'POST create model template' do
    context 'when invalid params' do
      it 'returns error when name is missing' do
        invalid_params = valid_params.except(:name)
        create(invalid_params, admin)
        expect_response(:unprocessable_entity)
      end

      it 'returns error when template_type is invalid' do
        invalid_params = valid_params.merge(template_type: 'invalid_type')
        create(invalid_params, admin)
        expect_response(:unprocessable_entity)
      end
    end

    context 'when reference_output_url is provided' do
      before do
        allow(External::Ragie).to receive(:new).and_return(double(create_document_from_url: { 'id' => 'doc_123' }))
      end

      let(:valid_params_with_url) do
        valid_params.merge(reference_output_url: 'https://example.com/reference_output')
      end

      it 'creates a model template and openai document' do
        create(valid_params_with_url, admin)
        expect_response(:created)

        expect(ModelTemplate.count).to eq(1)

        openai_file = OpenaiFile.find_by(object_id: response_data[:id])
        expect(openai_file).to be_present

        expect(openai_file.openai_file_id).to eq('doc_123')
        expect(openai_file.object_class).to eq('ModelTemplate')
        expect(openai_file.object_class_column).to eq('reference_output_url')
      end

      it 'creates a model template and openai document with rakamin service' do
        ENV['RAKAMIN_SERVICE_ENABLER'] = 'true'
        allow(External::RagService).to receive(:new).and_return(double(upload_document: { 'id' => 123 }))
        create(valid_params_with_url, admin)
        expect_response(:created)

        expect(ModelTemplate.count).to eq(1)

        openai_file = OpenaiFile.find_by(object_id: response_data[:id])
        expect(openai_file).to be_present

        expect(openai_file.openai_file_id).to eq('123')
        expect(openai_file.object_class).to eq('ModelTemplate')
        expect(openai_file.object_class_column).to eq('reference_output_url')
        ENV['RAKAMIN_SERVICE_ENABLER'] = 'false'
      end
    end

    context 'when file_id is provided' do
      let!(:uploaded_file) do
        OpenaiFile.create!(
          object_id: SecureRandom.uuid,
          object_class: 'RagDocument',
          object_class_column: 'reference_output',
          openai_file_id: 'rag_doc_123'
        )
      end

      let(:valid_params_with_file_id) do
        valid_params.merge(file_id: uploaded_file.id)
      end

      it 'creates a model template and associates the existing openai file' do
        create(valid_params_with_file_id, admin)
        expect_response(:created)

        expect(ModelTemplate.count).to eq(1)
        template = ModelTemplate.last

        uploaded_file.reload
        expect(uploaded_file.object_id).to eq(template.id.to_s)
        expect(uploaded_file.object_class).to eq('ModelTemplate')
        expect(uploaded_file.object_class_column).to eq('reference_output_url')
        expect(uploaded_file.openai_file_id).to eq('rag_doc_123')
      end

      it 'creates a model template without file_id when file_id is blank' do
        create(valid_params.merge(file_id: nil), admin)
        expect_response(:created)

        expect(ModelTemplate.count).to eq(1)
        template = ModelTemplate.last

        openai_file = OpenaiFile.find_by(
          object_id: template.id,
          object_class: 'ModelTemplate',
          object_class_column: 'reference_output_url'
        )
        expect(openai_file).to be_nil
      end

      it 'raises error when file_id does not exist' do
        create(valid_params.merge(file_id: 999_999), admin)
        expect_response(:not_found)
      end
    end

    it 'return created model template' do
      create(valid_params, admin)
      expect_response(:created)

      expect(response_data[:name]).to eq(valid_params[:name])
      expect(response_data[:description]).to eq(valid_params[:description])
      expect(response_data[:max_tokens]).to eq(valid_params[:max_tokens])
      expect(response_data[:model]).to eq(valid_params[:model])
      expect(response_data[:instruction]).to eq(valid_params[:instruction])
      expect(response_data[:prompt]).to eq(valid_params[:prompt])
      expect(response_data[:placeholder]).to eq(valid_params[:placeholder])
      expect(response_data[:template_type]).to eq(valid_params[:template_type])
      expect(response_data[:verified]).to eq(valid_params[:verified])
    end
  end
end
