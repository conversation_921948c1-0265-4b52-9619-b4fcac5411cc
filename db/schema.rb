# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_09_17_134357) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "hstore"
  enable_extension "plpgsql"
  enable_extension "vector"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "agent_workflow_node_runs", force: :cascade do |t|
    t.bigint "agent_workflow_node_id", null: false
    t.string "status", default: "pending", null: false
    t.string "error_message"
    t.string "error_code"
    t.jsonb "request_raw", default: {}
    t.jsonb "response_raw", default: {}
    t.jsonb "data", default: {}
    t.jsonb "usage_metadata"
    t.datetime "started_at"
    t.datetime "completed_at"
    t.datetime "failed_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "user_id"
    t.integer "agent_workflow_run_id"
    t.index ["agent_workflow_node_id"], name: "index_agent_workflow_node_runs_on_agent_workflow_node_id"
    t.index ["agent_workflow_run_id"], name: "index_agent_workflow_node_runs_on_agent_workflow_run_id"
    t.index ["discarded_at"], name: "index_agent_workflow_node_runs_on_discarded_at"
    t.index ["user_id"], name: "index_agent_workflow_node_runs_on_user_id"
  end

  create_table "agent_workflow_nodes", force: :cascade do |t|
    t.bigint "agent_workflow_id", null: false
    t.bigint "model_template_id"
    t.string "name"
    t.string "description"
    t.string "workflow_type", default: "merger", null: false
    t.jsonb "data", default: {}
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "knowledge_base_file_id"
    t.integer "order_level", default: 0
    t.string "reference_output"
    t.string "rules"
    t.integer "model_bank_id"
    t.index ["agent_workflow_id"], name: "index_agent_workflow_nodes_on_agent_workflow_id"
    t.index ["discarded_at"], name: "index_agent_workflow_nodes_on_discarded_at"
    t.index ["knowledge_base_file_id"], name: "index_agent_workflow_nodes_on_knowledge_base_file_id"
    t.index ["model_bank_id"], name: "index_agent_workflow_nodes_on_model_bank_id"
    t.index ["model_template_id"], name: "index_agent_workflow_nodes_on_model_template_id"
  end

  create_table "agent_workflow_runs", force: :cascade do |t|
    t.bigint "agent_workflow_id", null: false
    t.string "status", default: "pending", null: false
    t.jsonb "data", default: {}
    t.string "error_message"
    t.string "error_code"
    t.datetime "started_at"
    t.datetime "completed_at"
    t.datetime "failed_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "user_id"
    t.index ["agent_workflow_id"], name: "index_agent_workflow_runs_on_agent_workflow_id"
    t.index ["discarded_at"], name: "index_agent_workflow_runs_on_discarded_at"
    t.index ["user_id"], name: "index_agent_workflow_runs_on_user_id"
  end

  create_table "agent_workflows", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.jsonb "required_data", default: {}
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "created_by_id"
    t.integer "organization_id"
    t.index ["discarded_at"], name: "index_agent_workflows_on_discarded_at"
    t.index ["organization_id"], name: "index_agent_workflows_on_organization_id"
  end

  create_table "airwallex_tokens", force: :cascade do |t|
    t.text "token"
    t.datetime "expires_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_airwallex_tokens_on_discarded_at"
    t.index ["expires_at"], name: "index_airwallex_tokens_on_expires_at"
  end

  create_table "chats", force: :cascade do |t|
    t.integer "workspace_id"
    t.integer "model_id"
    t.string "name"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "compiled_prompt"
    t.string "chat_type", default: "general", null: false
    t.string "external_id"
    t.integer "source_model_template_id"
    t.string "expert_prompt"
    t.bigint "workspace_membership_workspace_id"
    t.bigint "workspace_membership_membership_id"
    t.index ["discarded_at"], name: "index_chats_on_discarded_at"
    t.index ["workspace_membership_membership_id"], name: "index_chats_on_workspace_membership_membership_id"
    t.index ["workspace_membership_workspace_id"], name: "index_chats_on_workspace_membership_workspace_id"
  end

  create_table "credit_histories", force: :cascade do |t|
    t.bigint "organization_id"
    t.string "action"
    t.decimal "monthly_credits", precision: 30, scale: 9
    t.decimal "purchased_credits", precision: 30, scale: 9
    t.bigint "invoice_id"
    t.datetime "action_at"
    t.string "user_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "message_ids", default: [], array: true
    t.index ["action_at"], name: "index_credit_histories_on_action_at"
    t.index ["discarded_at"], name: "index_credit_histories_on_discarded_at"
    t.index ["invoice_id"], name: "index_credit_histories_on_invoice_id"
    t.index ["organization_id"], name: "index_credit_histories_on_organization_id"
    t.index ["user_id"], name: "index_credit_histories_on_user_id"
  end

  create_table "invoices", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "status"
    t.integer "checkout_price"
    t.integer "checkout_price_decimal"
    t.integer "checkout_token_amounts"
    t.string "checkout_currency"
    t.string "payment_intent_id"
    t.string "payment_confirmation_request_id"
    t.string "payment_merchant_order_id"
    t.string "payment_request_id"
    t.string "payment_client_secret"
    t.string "payment_status"
    t.bigint "store_item_id"
    t.bigint "organization_id"
    t.string "user_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "expired_at"
    t.index ["discarded_at"], name: "index_invoices_on_discarded_at"
    t.index ["organization_id"], name: "index_invoices_on_organization_id"
    t.index ["store_item_id"], name: "index_invoices_on_store_item_id"
    t.index ["user_id"], name: "index_invoices_on_user_id"
  end

  create_table "knowledge_base_files", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.string "name", null: false
    t.text "description"
    t.string "file_url"
    t.string "filename", null: false
    t.string "content_type"
    t.bigint "file_size"
    t.boolean "is_active", default: true
    t.datetime "discarded_at"
    t.string "ragie_document_id"
    t.string "ragie_status", default: "pending"
    t.datetime "ragie_processed_at"
    t.text "ragie_error_message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "file_id"
    t.index ["discarded_at"], name: "index_knowledge_base_files_on_discarded_at"
    t.index ["organization_id", "is_active"], name: "index_knowledge_base_files_on_organization_id_and_is_active"
    t.index ["organization_id", "ragie_status"], name: "index_knowledge_base_files_on_organization_id_and_ragie_status"
    t.index ["organization_id"], name: "index_knowledge_base_files_on_organization_id"
    t.index ["ragie_document_id"], name: "index_knowledge_base_files_on_ragie_document_id"
    t.index ["ragie_status"], name: "index_knowledge_base_files_on_ragie_status"
  end

  create_table "memberships", force: :cascade do |t|
    t.integer "user_id"
    t.integer "organization_id"
    t.integer "role"
    t.string "code"
    t.string "invited_email"
    t.integer "organization_team_ids", default: [], array: true
    t.datetime "discarded_at"
    t.bigint "managed_organization_ids", default: [], array: true
    t.index ["discarded_at"], name: "index_memberships_on_discarded_at"
    t.index ["managed_organization_ids"], name: "index_memberships_on_managed_organization_ids", using: :gin
    t.index ["organization_team_ids"], name: "index_memberships_on_organization_team_ids"
  end

  create_table "messages", force: :cascade do |t|
    t.integer "chat_id"
    t.string "content"
    t.string "sender"
    t.datetime "created_at"
    t.string "image_url"
    t.string "file_url", default: ""
    t.integer "tokens_used", default: 0
    t.string "status_on_thread", default: "present", null: false
    t.datetime "discarded_at"
    t.string "model_used", default: "gpt-4o", null: false
    t.decimal "credits_used", precision: 30, scale: 9
    t.jsonb "raw_request", default: {}
    t.json "raw_responses", default: []
    t.integer "file_id"
    t.index ["discarded_at"], name: "index_messages_on_discarded_at"
  end

  create_table "model_banks", force: :cascade do |t|
    t.string "name", null: false
    t.string "code", null: false
    t.float "input_rate", default: 0.0, null: false
    t.float "output_rate", default: 0.0, null: false
    t.float "web_search_rate", default: 0.0, null: false
    t.float "image_rate", default: 0.0, null: false
    t.float "file_rate", default: 0.0, null: false
    t.string "status", default: "active", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_model_banks_on_discarded_at"
    t.index ["name"], name: "index_model_banks_on_name"
    t.index ["status"], name: "index_model_banks_on_status"
  end

  create_table "model_ratings", force: :cascade do |t|
    t.integer "rating"
    t.string "user_id"
    t.integer "model_template_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "comment"
    t.string "feedback"
    t.datetime "discarded_at"
    t.bigint "message_id"
    t.index ["discarded_at"], name: "index_model_ratings_on_discarded_at"
    t.index ["message_id"], name: "index_model_ratings_on_message_id"
  end

  create_table "model_template_ins", force: :cascade do |t|
    t.bigint "model_template_id", null: false
    t.string "name"
    t.string "description"
    t.string "input_reference_url", default: ""
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "order", default: 0, null: false
    t.string "weight"
    t.integer "file_id"
    t.index ["model_template_id"], name: "index_model_template_ins_on_model_template_id"
  end

  create_table "model_template_variables", force: :cascade do |t|
    t.bigint "model_template_id", null: false
    t.string "name"
    t.string "description"
    t.string "weight"
    t.string "references"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "variable_reference_url", default: ""
    t.datetime "discarded_at"
    t.integer "order", default: 0, null: false
    t.integer "file_id"
    t.index ["discarded_at"], name: "index_model_template_variables_on_discarded_at"
    t.index ["model_template_id"], name: "index_model_template_variables_on_model_template_id"
  end

  create_table "model_templates", force: :cascade do |t|
    t.string "name", null: false
    t.string "description", null: false
    t.integer "max_tokens", default: 2000
    t.float "temperature", default: 1.0
    t.string "model", default: "gpt-4o-mini"
    t.string "instruction", default: "You are ChatGPT, answer as helpful as possible!"
    t.string "prompt"
    t.string "placeholder"
    t.string "template_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "verified", default: false
    t.text "category", default: [], array: true
    t.boolean "organization_prompt", default: false
    t.boolean "draft", default: true
    t.string "reference_output_url", default: ""
    t.integer "test_prompt_chat_id"
    t.bigint "organization_id"
    t.bigint "workspace_id"
    t.string "user_id"
    t.bigint "template_category_id"
    t.datetime "discarded_at"
    t.bigint "workspaces_membership_id"
    t.bigint "organization_team_id"
    t.bigint "parent_id"
    t.integer "child_count", default: 0
    t.boolean "in_bank", default: false
    t.string "bank_added_by"
    t.datetime "bank_added_at"
    t.text "bank_notes"
    t.float "rating", default: 0.0
    t.integer "file_id"
    t.index ["bank_added_by"], name: "index_model_templates_on_bank_added_by"
    t.index ["discarded_at"], name: "index_model_templates_on_discarded_at"
    t.index ["in_bank"], name: "index_model_templates_on_in_bank"
    t.index ["organization_id"], name: "index_model_templates_on_organization_id"
    t.index ["organization_team_id"], name: "index_model_templates_on_organization_team_id"
    t.index ["parent_id"], name: "index_model_templates_on_parent_id"
    t.index ["template_category_id"], name: "index_model_templates_on_template_category_id"
    t.index ["user_id"], name: "index_model_templates_on_user_id"
    t.index ["workspace_id"], name: "index_model_templates_on_workspace_id"
    t.index ["workspaces_membership_id"], name: "index_model_templates_on_workspaces_membership_id"
  end

  create_table "models", force: :cascade do |t|
    t.integer "organization_id"
    t.string "name"
    t.string "model", default: "gpt-4o-mini"
    t.string "instruction"
    t.float "temperature"
    t.float "top_p"
    t.float "frequency_penalty"
    t.float "presence_penalty"
    t.integer "max_tokens"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "openai_assistant_id"
    t.boolean "file_search", default: true
    t.boolean "code_interpreter", default: false
    t.bigint "model_template_id"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_models_on_discarded_at"
    t.index ["model_template_id"], name: "index_models_on_model_template_id"
  end

  create_table "openai_chats", force: :cascade do |t|
    t.bigint "chat_id", null: false
    t.string "openai_assistant_id"
    t.string "openai_thread_id"
    t.string "openai_runner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["chat_id"], name: "index_openai_chats_on_chat_id"
    t.index ["discarded_at"], name: "index_openai_chats_on_discarded_at"
  end

  create_table "openai_files", force: :cascade do |t|
    t.integer "object_id"
    t.string "object_class"
    t.string "object_class_column"
    t.string "openai_file_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_openai_files_on_discarded_at"
  end

  create_table "organization_teams", force: :cascade do |t|
    t.bigint "organization_id"
    t.string "name"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_organization_teams_on_discarded_at"
    t.index ["organization_id"], name: "index_organization_teams_on_organization_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "name"
    t.string "logo_url"
    t.datetime "discarded_at"
    t.boolean "superuser_privilege", default: false
    t.string "code", default: "", null: false
    t.string "created_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_partner", default: false
    t.index ["code"], name: "index_organizations_on_code", unique: true
    t.index ["created_by_id"], name: "index_organizations_on_created_by_id"
    t.index ["discarded_at"], name: "index_organizations_on_discarded_at"
  end

  create_table "organizations_plans_thresholds", force: :cascade do |t|
    t.decimal "purchased_credits", precision: 30, scale: 9, default: "0.0", null: false
    t.integer "max_workspaces"
    t.integer "max_members"
    t.integer "organization_id"
    t.integer "monthly_credits_refresh", default: 0, null: false
    t.datetime "discarded_at"
    t.integer "refresh_date", default: 0, null: false
    t.decimal "remaining_monthly_credits", precision: 30, scale: 9, default: "0.0", null: false
    t.integer "max_knowledge_base_files", default: 10, null: false
    t.index ["discarded_at"], name: "index_organizations_plans_thresholds_on_discarded_at"
  end

  create_table "organizations_subscriptions", force: :cascade do |t|
    t.integer "organization_id"
    t.string "subscription_id"
    t.string "customer_id"
  end

  create_table "partner_assigned_model_templates", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "model_template_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_partner_assigned_model_templates_on_discarded_at"
    t.index ["model_template_id"], name: "index_partner_assigned_model_templates_on_model_template_id"
    t.index ["organization_id", "model_template_id"], name: "index_unique_organization_model_template_partners", unique: true
    t.index ["organization_id"], name: "index_partner_assigned_model_templates_on_organization_id"
  end

  create_table "plans", primary_key: "variant_id", force: :cascade do |t|
    t.string "name"
    t.string "price"
    t.integer "product_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "products", primary_key: "product_id", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "badge"
    t.jsonb "features"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "monthly_tokens", default: 0
  end

  create_table "prompt_eval_results", force: :cascade do |t|
    t.bigint "prompt_eval_id", null: false
    t.bigint "model_bank_id", null: false
    t.string "result_text", null: false
    t.string "status", default: "pending", null: false
    t.string "error_message"
    t.jsonb "response_raw", default: {}
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_prompt_eval_results_on_discarded_at"
    t.index ["model_bank_id"], name: "index_prompt_eval_results_on_model_bank_id"
    t.index ["prompt_eval_id"], name: "index_prompt_eval_results_on_prompt_eval_id"
  end

  create_table "prompt_evals", force: :cascade do |t|
    t.string "prompt", null: false
    t.jsonb "params", default: {}
    t.jsonb "request_raw"
    t.string "user_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "model_template_id"
    t.index ["discarded_at"], name: "index_prompt_evals_on_discarded_at"
    t.index ["model_template_id"], name: "index_prompt_evals_on_model_template_id"
    t.index ["user_id"], name: "index_prompt_evals_on_user_id"
  end

  create_table "store_items", force: :cascade do |t|
    t.string "name"
    t.string "store_item_type", default: "one_time_purchase_token", null: false
    t.integer "token_amounts"
    t.integer "price", default: 0, null: false
    t.integer "price_decimal", default: 0, null: false
    t.string "currency", default: "USD", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_store_items_on_discarded_at"
  end

  create_table "subscriptions", force: :cascade do |t|
    t.integer "variant_id"
    t.string "status"
    t.boolean "cancel_at_period_end"
    t.datetime "created_at"
    t.datetime "trial_starts_at"
    t.datetime "trial_ends_at"
    t.integer "billing_anchor"
    t.datetime "ends_at"
    t.datetime "renews_at"
    t.string "update_payment_method_url"
  end

  create_table "template_categories", force: :cascade do |t|
    t.string "name"
    t.boolean "validated", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "general_category", default: false
    t.bigint "organization_id"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_template_categories_on_discarded_at"
    t.index ["organization_id"], name: "index_template_categories_on_organization_id"
  end

  create_table "token_requests", force: :cascade do |t|
    t.string "email"
    t.string "user_id"
    t.string "purpose"
    t.string "request_status"
    t.string "request_code"
    t.datetime "request_expiry_date"
    t.datetime "requested_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_token_requests_on_discarded_at"
    t.index ["request_expiry_date"], name: "index_token_requests_on_request_expiry_date"
    t.index ["requested_at"], name: "index_token_requests_on_requested_at"
    t.index ["user_id"], name: "index_token_requests_on_user_id"
  end

  create_table "user_invitations", force: :cascade do |t|
    t.string "email"
    t.string "invitation_status"
    t.string "invitation_code"
    t.datetime "invitation_expiry_date"
    t.bigint "organization_id"
    t.bigint "invited_to_organization_team_id"
    t.bigint "invited_by_membership_id"
    t.integer "role"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "managed_organization_ids", default: [], array: true
    t.index ["discarded_at"], name: "index_user_invitations_on_discarded_at"
    t.index ["invited_by_membership_id"], name: "index_user_invitations_on_invited_by_membership_id"
    t.index ["invited_to_organization_team_id"], name: "index_user_invitations_on_invited_to_organization_team_id"
    t.index ["organization_id"], name: "index_user_invitations_on_organization_id"
  end

  create_table "users", force: :cascade do |t|
    t.text "photo_url"
    t.text "display_name"
    t.boolean "onboarded", default: false
    t.string "email"
    t.string "password_digest", default: "null"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_users_on_discarded_at"
    t.index ["email"], name: "index_users_on_email", unique: true
  end

  create_table "web_search_results", force: :cascade do |t|
    t.bigint "message_id", null: false
    t.string "url", null: false
    t.string "title"
    t.text "content"
    t.integer "start_index"
    t.integer "end_index"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.string "image"
    t.string "site_name"
    t.index ["discarded_at"], name: "index_web_search_results_on_discarded_at"
    t.index ["message_id"], name: "index_web_search_results_on_message_id"
    t.index ["url"], name: "index_web_search_results_on_url"
  end

  create_table "workspaces", force: :cascade do |t|
    t.string "name"
    t.integer "max_workspaces"
    t.integer "max_members"
    t.integer "organization_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_workspaces_on_discarded_at"
  end

  create_table "workspaces_memberships", force: :cascade do |t|
    t.integer "workspace_id"
    t.integer "membership_id"
    t.string "role"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_workspaces_memberships_on_discarded_at"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "agent_workflow_node_runs", "agent_workflow_nodes"
  add_foreign_key "agent_workflow_nodes", "agent_workflows"
  add_foreign_key "agent_workflow_nodes", "knowledge_base_files"
  add_foreign_key "agent_workflow_nodes", "model_banks"
  add_foreign_key "agent_workflow_nodes", "model_templates"
  add_foreign_key "agent_workflow_runs", "agent_workflows"
  add_foreign_key "agent_workflows", "organizations"
  add_foreign_key "knowledge_base_files", "organizations"
  add_foreign_key "model_ratings", "messages"
  add_foreign_key "model_templates", "model_templates", column: "parent_id", on_delete: :nullify
  add_foreign_key "partner_assigned_model_templates", "model_templates"
  add_foreign_key "partner_assigned_model_templates", "organizations"
  add_foreign_key "prompt_eval_results", "model_banks"
  add_foreign_key "prompt_eval_results", "prompt_evals"
  add_foreign_key "prompt_evals", "model_templates"
  add_foreign_key "web_search_results", "messages"
end
