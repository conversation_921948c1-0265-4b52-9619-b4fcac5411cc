# frozen_string_literal: true

module V1
  class PlatformRoleOutput < ApiOutput
    def format
      {
        id: @object.id,
        email: email,
        role: role,
        account_status: account_status,
        user: user_output,
        organization: organization_output,
        primary_organization: managed_organizations_output.first,
        managed_organizations: managed_organizations_output.drop(1),
        organization_team: organization_team
      }
    end

    private

    def managed_organizations_output
      if @object.is_a?(Membership)
        return [] if @object.managed_organizations.blank?

        organizations = @object.managed_organizations.compact
        organizations.map do |organization|
          {
            id: organization.id,
            name: organization.name,
            code: organization.code
          }
        end
      else
        # For UserInvitation, get managed organizations from managed_organization_ids
        return [] if @object.managed_organization_ids.blank?

        organizations = Organization.where(id: @object.managed_organization_ids)
        organizations.map do |organization|
          {
            id: organization.id,
            name: organization.name,
            code: organization.code
          }
        end
      end
    end

    def user_output
      if @object.is_a?(Membership)
        user = @object.user
        return nil if user.blank?

        {
          id: user.id,
          display_name: user.display_name,
          name: user.display_name,
          photo_url: user.photo_url,
          email: user.email
        }
      else
        # For UserInvitation, return basic info since user doesn't exist yet
        {
          id: nil,
          display_name: nil,
          name: nil,
          photo_url: nil,
          email: @object.email
        }
      end
    end

    def organization_output
      organization = @object.organization

      return nil if organization.blank?

      {
        id: organization.id,
        name: organization.name,
        code: organization.code
      }
    end

    def managed_organizations
      @options[:managed_organizations] || []
    end

    def email
      if @object.is_a?(Membership)
        @object.user.email
      else
        @object.email
      end
    end

    def role
      if @object.is_a?(Membership)
        @object.membership_role
      else
        Membership.list_membership_roles[@object.role] || 'member'
      end
    end

    def organization_team
      if @object.is_a?(Membership)
        @object.organization_team
      else
        @object.invited_to_organization_team
      end
    end

    def account_status
      if @object.is_a?(Membership)
        'activated'
      else
        'invited'
      end
    end
  end
end
