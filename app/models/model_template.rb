# frozen_string_literal: true

class ModelTemplate < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization
  belongs_to :user
  belongs_to :template_category, optional: true
  belongs_to :organization_team, optional: true
  belongs_to :parent, class_name: 'ModelTemplate', optional: true
  has_many :models, dependent: :destroy
  has_many :model_template_variables, dependent: :destroy
  has_many :model_template_ins, dependent: :destroy
  has_many :model_ratings, dependent: :destroy
  has_many :children, class_name: 'ModelTemplate', foreign_key: 'parent_id', dependent: :nullify
  has_many :partner_assigned_model_templates, dependent: :destroy

  validates :name, presence: true
  validates :max_tokens, presence: true, numericality: { only_integer: true, greater_than: 0 }
  validates :temperature, presence: true, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 2 }
  validates :model, presence: true

  scope :organization_prompts, -> { where(organization_prompt: true) }
  scope :team_templates, -> { where.not(organization_team_id: nil) }
  scope :personal_templates, -> { where(organization_prompt: false, organization_team_id: nil) }
  scope :parent_templates, -> { where(parent_id: nil) }
  scope :child_templates, -> { where.not(parent_id: nil) }
  scope :bank_templates, -> { where(in_bank: true) }
  scope :non_bank_templates, -> { where(in_bank: false) }

  belongs_to :test_prompt_chat, optional: true, class_name: 'Chat', foreign_key: 'test_prompt_chat_id'
  belongs_to :workspaces_membership, optional: true

  enum template_type: string_enum('default', 'expert', 'workflow')

  # validate :valid_test_prompt_chat_id

  # def valid_test_prompt_chat_id
  #   valid = self.test_prompt_chat_id.nil? || Chat.exists?(id: self.test_prompt_chat_id)

  #   self.errors.add :base, 'Test prompt chat does not exist' if !valid
  # end

  def is_parent?
    parent_id.nil?
  end

  def is_child?
    parent_id.present?
  end

  def add_to_bank!(user_id, notes = nil)
    update!(
      in_bank: true,
      verified: true, # Auto-verify when added to bank
      bank_added_by: user_id.to_s,
      bank_added_at: Time.current,
      bank_notes: notes
    )
  end

  def remove_from_bank!
    update!(
      in_bank: false,
      bank_added_by: nil,
      bank_added_at: nil,
      bank_notes: nil
    )
  end

  def update_child_count!
    update!(child_count: children.count)
  end
  
  def bank_added_by_user
    User.find_by(id: bank_added_by) if bank_added_by.present?
  end
end
