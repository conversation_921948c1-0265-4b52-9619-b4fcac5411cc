# frozen_string_literal: true

module V1
  class ModelTemplatesController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      options = {
        current_user: current_user,
        variables: result.variables,
        template_categories: result.template_categories,
        number_of_used_times: result.number_of_used_times,
        organization_teams: result.organization_teams,
        ratings: result.ratings,
        users_with_assigned_team: result.users_with_assigned_team,
        children: result.children,
        organizations: result.organizations,
        parent_templates: result.parent_templates,
        bank_added_by_users: result.bank_added_by_users,
        assigned_organizations: result.assigned_organizations,
        partner_assigned_organizations: result.partner_assigned_organizations,
        show_editable: true
      }

      render_json_array result.templates, **options
    end

    def show
      result = service.show(params[:id])

      options = {
        current_user: current_user,
        variables: result.variables,
        instruction_inputs: result.instruction_inputs,
        template_categories: result.template_categories,
        organization_teams: result.organization_teams,
        ratings: result.ratings,
        children: result.children
      }

      render_json result.template, **options
    end

    def create
      input = ::V1::ModelTemplateCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(input.output)

      render_json result.template, status: :created,
                                   current_user: current_user,
                                   variables: result.variables,
                                   template_categories: result.template_categories
    end

    def update
      input = ::V1::ModelTemplateUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.template, current_user: current_user,
                                   variables: result.variables,
                                   template_categories: result.template_categories
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    def list_comments
      result = service.list_comments(params[:model_template_id])

      render_json_array result.comments,
                        ::V1::ModelRatingOutput,
                        number_of_used_times: result.number_of_used_times,
                        users: result.users
    end

    def duplicate
      result = service.duplicate(params[:model_template_id])

      render_json result.template,
                  current_user: current_user,
                  variables: result.variables,
                  template_categories: result.template_categories,
                  instruction_inputs: result.instruction_inputs
    end

    def system_prompt
      system_prompt = service.system_prompt(params[:id])
      render_json system_prompt, use: :system_prompt_format
    end

    def add_to_bank
      input = ::V1::AddToBankInput.new(request_body)
      validate! input, capture_failure: true

      result = service.add_to_bank(params[:model_template_id], input.output[:notes])

      render_json result.template, current_user:, message: result.message
    end

    def remove_from_bank
      result = service.remove_from_bank(params[:model_template_id])

      render_json result.template, current_user:, message: result.message
    end

    def duplicate_to_organizations
      input = ::V1::ModelTemplateDuplicateToOrganizationsInput.new(request_body)
      validate! input, capture_failure: true

      result = service.duplicate_to_organizations(input.output[:parent_id], input.output[:organization_ids])

      render_json result.parent_template,
                  ::V1::ModelTemplateOutput,
                  use: :format, status: :ok,
                  current_user: current_user,
                  variables: result.parent_template.model_template_variables,
                  instruction_inputs: result.parent_template.model_template_ins,
                  template_categories: [result.parent_template.template_category].compact,
                  organization_teams: [result.parent_template.organization_team].compact,
                  children: result.duplicated_templates
    end

    private

    def default_output
      ::V1::ModelTemplateOutput
    end

    def service
      @service ||= ::ModelTemplateService.new(current_user)
    end
  end
end
