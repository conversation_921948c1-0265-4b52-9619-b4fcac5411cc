# frozen_string_literal: true

module V1
  class UploadRagDocumentsController < ApiController
    authorize_auth_token! :all

    def create
      input = V1::UploadRagDocumentInput.new(request_body)
      validate! input, capture_failure: true

      document = service.upload_document(input.output)
      render_json document, status: :created
    end

    def show
      document = service.status(params[:id])
      render_json document
    end

    private

    def service
      @service ||= ::UploadRagDocumentService.new(current_user)
    end

    def default_output
      ::V1::UploadRagDocumentOutput
    end
  end
end
