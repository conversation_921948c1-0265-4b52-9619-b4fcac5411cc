# frozen_string_literal: true

class ModelTemplateService < AppService
  def index(params)
    params[:organization_prompt] = as_boolean(params[:organization_prompt]) if params[:organization_prompt].present?
    params[:draft] = as_boolean(params[:draft]) if params[:draft].present?
    params[:verified] = as_boolean(params[:verified]) if params[:verified].present?
    params[:template_type] = Array(params[:template_type]) if params[:template_type]
    params[:in_bank] = as_boolean(params[:in_bank]) if params[:in_bank].present?

    # Sanitize search parameter
    params[:search] = params[:search].strip if params[:search].present?

    templates = ::ModelTemplates.new

    filter = params.slice(
      :template_type,
      :organization_prompt,
      :draft,
      :verified,
      :template_category_id,
      :search,
      :disable_pagination,
      :organization_id,
      :parent_only,
      :child_only,
      :parent_id,
      :child_id,
      :page,
      :per_page,
      :in_bank,
      :duplicated_from,
      :duplicated_to
    )

    # Only filter by organization_id if not admin, super_admin_platform, owner_platform and no specific organization_id provided
    unless platform_admin?
      filter = filter.merge(
        organization_id: @user.membership.organization_id
      )
    end

    # Partner admin can only see templates from organizations they created
    filter = filter.merge(organization_id: user_allowed_org_ids) if partner_admin?

    # Enhanced parent-child filtering
    if params[:parent_id].present?
      # Get children of a specific parent
      filter = filter.merge(parent_id: params[:parent_id])
    elsif params[:child_id].present?
      # Get parent of a specific child
      child_template = ModelTemplate.find(params[:child_id])
      if child_template.parent_id.present?
        filter = filter.merge(id: child_template.parent_id)
      else
        # If child has no parent, return empty result
        return OpenStruct.new(
          templates: ModelTemplate.none,
          variables: ModelTemplateVariable.none,
          template_categories: TemplateCategory.none,
          number_of_used_times: {},
          organization_teams: OrganizationTeam.none,
          ratings: [],
          users_with_assigned_team: User.none,
          children: ModelTemplate.none,
          organizations: Organization.none,
          parent_templates: []
        )
      end
    elsif params[:parent_only].present?
      # Get only parent templates (no parent_id)
      filter = filter.merge(parent_id: nil)
    elsif params[:child_only].present?
      # Get only child templates (has parent_id)
      filter = filter.merge(parent_id_not: nil)
    end

    # Handle duplicated_from filter (templates duplicated from a specific organization)
    if params[:duplicated_from].present?
      parent_template_ids = ModelTemplate.where(organization_id: params[:duplicated_from]).pluck(:id)
      filter = filter.merge(parent_id: parent_template_ids)
    end

    # Handle duplicated_to filter (templates duplicated to a specific organization)
    if params[:duplicated_to].present?
      child_template_ids = ModelTemplate.where(organization_id: params[:duplicated_to]).pluck(:parent_id)
      filter = filter.merge(id: child_template_ids) if child_template_ids.any?
    end

    if %w[member team_admin].include?(@user.membership.membership_role)
      org_team_ids = @user.membership.organization_team_ids
      filter_team_ids = [nil] + org_team_ids
      filter = filter.merge(
        organization_team_id: filter_team_ids.uniq
      )
    end

    filtered_result_templates = templates.filter(filter)

    template_ids = filtered_result_templates.pluck(:id)
    variables = ModelTemplateVariable.where(model_template_id: template_ids)
    template_categories = TemplateCategory.where(id: filtered_result_templates.pluck(:template_category_id).compact.uniq)
    number_of_used_times = Message.joins(:chat)
                                  .where(
                                    chats: {
                                      source_model_template_id: template_ids
                                    },
                                    sender: 'user'
                                  )
                                  .group('chats.source_model_template_id')
                                  .count
    ratings = ModelRating.select('model_template_id, COUNT(id) as c, AVG(COALESCE(rating, 0)) as avg')
                         .where(model_template_id: template_ids)
                         .group(:model_template_id)

    organization_teams = OrganizationTeam.where(id: filtered_result_templates.pluck(:organization_team_id).compact.uniq)

    users_with_assigned_team = User.select('users.*, array_agg(m.unnest_org_team_ids) as organization_team_ids_agg')
                                   .joins('LEFT JOIN (SELECT mem.user_id, mem.organization_id, unnest(mem.organization_team_ids) as unnest_org_team_ids FROM memberships mem) AS m ON m.user_id = users.id')
                                   .where(id: filtered_result_templates.pluck(:user_id).compact.uniq)
                                   .group('users.id')

    # Get children for parent templates
    children = ModelTemplate.where(parent_id: template_ids)

    # Get parent templates for parent names
    parent_ids = filtered_result_templates.pluck(:parent_id).compact.uniq
    parent_templates = parent_ids.any? ? ModelTemplate.where(id: parent_ids) : []

    # Get organizations for organization names (include main templates, children, and parent templates)
    main_org_ids = filtered_result_templates.pluck(:organization_id).compact.uniq
    child_org_ids = children.pluck(:organization_id).compact.uniq
    parent_org_ids = parent_templates.pluck(:organization_id).compact.uniq
    all_org_ids = (main_org_ids + child_org_ids + parent_org_ids).uniq
    organizations = Organization.where(id: all_org_ids)

    # Get users who added templates to bank
    bank_added_by_users = User.where(id: filtered_result_templates.pluck(:bank_added_by).compact.uniq)

    partner_assigned_organizations = PartnerAssignedModelTemplate.where(model_template_id: template_ids)
    assigned_organizations = Organization.where(id: partner_assigned_organizations.pluck(:organization_id))

    OpenStruct.new(
      templates: filtered_result_templates,
      variables: variables,
      template_categories: template_categories,
      number_of_used_times: number_of_used_times,
      organization_teams: organization_teams,
      ratings: ratings,
      users_with_assigned_team: users_with_assigned_team,
      children: children,
      organizations: organizations,
      parent_templates: parent_templates,
      bank_added_by_users: bank_added_by_users,
      assigned_organizations:,
      partner_assigned_organizations:
    )
  end

  def show(id)
    template = ModelTemplate.find(id)
    # Allow admin to view any template
    authorize! template_ownership(template)

    ratings = ModelRating.select('model_template_id, COUNT(id) as c, AVG(COALESCE(rating, 0)) as avg')
                         .where(model_template_id: template.id)
                         .group(:model_template_id)

    # Get children if this is a parent template
    children = template.is_parent? ? template.children : []

    model_inputs = ModelTemplateIn.where(model_template_id: template.id)

    if template.template_type == 'workflow'
      workflow_id = AgentWorkflowNode.find_by(model_template_id: template.id)&.agent_workflow_id
      exist! workflow_id, on_error: 'Agent is workflow but not registered'

      agent_node_model_template_ids = AgentWorkflowNode.where(agent_workflow_id: workflow_id, workflow_type: :agent)
                                                       .pluck(:model_template_id)
      model_inputs = ModelTemplateIn.where(model_template_id: agent_node_model_template_ids)
    end

    OpenStruct.new(
      template: template,
      variables: template.model_template_variables,
      instruction_inputs: model_inputs,
      template_categories: TemplateCategory.where(id: template.template_category_id),
      organization_teams: OrganizationTeam.where(id: template.organization_team_id),
      ratings: ratings,
      children: children
    )
  end

  def create(params)
    # verify org membership & roles
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user,
                          %w[super_user owner super_admin team_admin member admin partner_admin])

    # Check if trying to create bank template
    in_bank = as_boolean(params[:in_bank])
    authorize_user_roles!(@user, %w[admin super_admin_platform owner_platform partner_admin]) if in_bank

    organization_id = if params[:organization_id].present? && (platform_admin? || partner_admin?)
                        params[:organization_id]
                      else
                        @user.membership.organization_id
                      end

    params[:organization_id] = organization_id
    params[:user_id] = @user.id

    # Partner admin can only create bank templates from organizations they created
    if in_bank && @user.role == 'partner_admin'
      @user.id.is_a?(Integer) ? @user.id.to_s : @user.id
      authorize! organization_ownership(Organization.find(organization_id)),
                 on_error: 'You can only create bank templates from organizations you created'
    end

    # either organization_prompt or organization_team template, not both
    is_org_template = as_boolean(params[:organization_prompt])
    is_org_team_id_present = params[:organization_team_id].present?

    assert! !(is_org_template && is_org_team_id_present),
            on_error: 'Cannot assign as organization template & team template simultaneously'

    # if organization_team template
    # verify user & organization_team template's assigned team
    if is_org_team_id_present && %w[team_admin member].include?(membership.membership_role)
      authorize! membership.organization_team_ids.compact.present?, on_error: 'You are not assigned to any team!'

      params[:organization_team_id] = ([params[:organization_team_id].to_i] & membership.organization_team_ids).first

      authorize! params[:organization_team_id].present?, on_error: 'You are not assigned to selected team!'
    end

    # setup non-modifiable data
    template = ModelTemplate.new
    # Allow admin, super_admin_platform, and owner_platform to specify organization_id

    if params[:template_category_id].present?
      template_category = TemplateCategory.find(params[:template_category_id])
      authorize! template_category_ownership(template_category)
    end

    if params[:organization_team_id].present?
      org_team = OrganizationTeam.find(params[:organization_team_id])
      authorize! platform_admin? || organization_team_ownership(org_team)
    end

    ActiveRecord::Base.transaction do
      # Remove in_bank from params before creating template
      bank_notes = params.delete(:in_bank) ? params.delete(:bank_notes) : nil

      template = ModelTemplate.create!(params.except(:v2))

      # Add to bank if requested
      template.add_to_bank!(@user.id.to_s, bank_notes) if in_bank

      system_prompt = build_system_prompt(template)

      file_id = template.file_id

      if file_id.present?
        OpenaiFile.find(file_id).update!(
          object_id: template.id,
          object_class: template.class.name,
          object_class_column: 'reference_output_url'
        )
      end

      Model.models.values.each do |m|
        Model.create!(
          organization_id: template.organization_id,
          name: "#{template.name} #{m}",
          model: m,
          instruction: system_prompt,
          temperature: template.temperature,
          max_tokens: template.max_tokens,
          model_template_id: template.id
        )
      end
    end

    OpenStruct.new(
      template: template,
      variables: template.model_template_variables,
      template_categories: TemplateCategory.where(id: template.template_category_id)
    )
  end

  def update(id, params)
    # verify org membership & roles
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin member partner_admin])

    # exclude non-modifiable data
    params.delete(:organization_id) unless platform_admin?
    params.delete(:workspace_id)
    params.delete(:user_id)

    # TODO: Refactor
    # either organization_prompt or organization_team template, not both
    is_org_template = as_boolean(params[:organization_prompt])
    is_org_team_id_present = params[:organization_team_id].present?

    assert! !(is_org_template && is_org_team_id_present),
            on_error: 'Cannot assign as organization template & team template simultaneously'

    # if organization_team template
    # verify user & organization_team template's assigned team
    if is_org_team_id_present && %w[team_admin member].include?(membership.membership_role)
      authorize! membership.organization_team_ids.compact.present?, on_error: 'You are not assigned to any team!'

      params[:organization_team_id] = ([params[:organization_team_id].to_i] & membership.organization_team_ids).first
      authorize! params[:organization_team_id].present?, on_error: 'You are not assigned to selected team!'
    end

    template = ModelTemplate.find(id)
    authorize! template_ownership(template)
    authorize! template_user_authority(template, membership)

    organization_id = template.organization_id
    @ragie = ::External::Ragie.new(organization_id)
    @rag_service = ::External::RagService.new(organization_id)

    if params[:template_category_id].present?
      template_category = TemplateCategory.find(params[:template_category_id])
      authorize! template_category_ownership(template_category)
    end

    if params[:organization_team_id].present?
      org_team = OrganizationTeam.where(id: params[:organization_team_id])
      authorize! organization_team_ownership(org_team)
    end

    current_file_id = template.file_id

    ActiveRecord::Base.transaction do
      template.update!(params.except(:v2))
      system_prompt = build_system_prompt(template)

      list_models = Model.models.values
      models = Model.where(model_template_id: template.id)

      models.each do |model|
        model.update!(
          name: "#{template.name} #{model.model}",
          instruction: system_prompt,
          temperature: template.temperature,
          max_tokens: template.max_tokens
        )
      end

      # Create model if there are model that is not created yet
      not_created_models = list_models - models.pluck(:model).uniq.compact
      unless not_created_models.empty?
        not_created_models.each do |m|
          Model.create!(
            organization_id: template.organization_id,
            name: "#{template.name} #{m}",
            model: m,
            instruction: system_prompt,
            temperature: template.temperature,
            max_tokens: template.max_tokens,
            model_template_id: template.id
          )
        end
      end

      result = OpenStruct.new(
        template: template,
        variables: template.model_template_variables,
        template_categories: TemplateCategory.where(id: template.template_category_id)
      )

      latest_file_id = template.file_id

      next result if current_file_id == latest_file_id

      openai_file = OpenaiFile.find_by(
        object_id: template.id,
        object_class: template.class.name,
        object_class_column: 'reference_output_url'
      )

      openai_file&.discard! if openai_file.present?

      next result if latest_file_id.blank?

      OpenaiFile.find(latest_file_id).update!(
        object_id: template.id,
        object_class: template.class.name,
        object_class_column: 'reference_output_url'
      )

      result
    end
  end

  def destroy(id)
    verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin member partner_admin])

    template = ModelTemplate.find(id)
    authorize! template_ownership(template)

    ActiveRecord::Base.transaction do
      Model.where(model_template_id: template.id).update_all(discarded_at: Time.current)

      # Delete reference output
      OpenaiFile.find_by(
        object_id: template.id,
        object_class: template.class.name,
        object_class_column: 'reference_output_url'
      ).try(:discard!)

      # Delete template variables
      template_variables = ModelTemplateVariable.where(model_template_id: template.id)
      OpenaiFile.where(
        object_id: template_variables.pluck(:id),
        object_class: 'ModelTemplateVariable',
        object_class_column: 'variable_reference_url'
      ).update_all(discarded_at: time_current)
      template_variables.update_all(discarded_at: time_current)

      # If this is a parent template, remove parent_id from children
      ModelTemplate.where(parent_id: template.id).update_all(parent_id: nil) if template.is_parent?

      template.discard!
    end
  end

  def list_comments(id)
    template = ModelTemplate.find(id)
    authorize! template_ownership(template)

    comments = ModelRating.where(model_template_id: id)

    joins = 'INNER JOIN chats ON messages.chat_id = chats.id' +
            ' LEFT JOIN workspaces_memberships wm ON wm.workspace_id = chats.workspace_membership_workspace_id AND wm.membership_id = chats.workspace_membership_membership_id' +
            ' LEFT JOIN memberships m ON m.id = wm.membership_id'

    number_of_used_times = Message.joins(joins)
                                  .where(chats: { source_model_template_id: id }, sender: 'user')
                                  .group('m.user_id')
                                  .count

    if number_of_used_times[nil].present?
      # Create a dummy record to hold used count thats not held by anyone
      other_comment_record = OpenStruct.new(
        id: nil,
        rating: 0,
        user_id: nil,
        model_template_id: id.to_i,
        comment: nil,
        feedback: nil
      )

      comments += [other_comment_record]
    end

    users = User.where(id: comments.pluck(:user_id))
    OpenStruct.new(
      template: template,
      comments: comments,
      number_of_used_times: number_of_used_times,
      users: users
    )
  end

  def update_parent_template(id, params)
    authorize_user_roles!(@user, %w[admin super_admin_platform owner_platform partner_admin])

    template = ModelTemplate.find(id)

    # Partner admin can only update templates in organizations they created
    if partner_admin?
      authorize! template_ownership(template), on_error: 'You are not authorized to update this template'
    end

    params[:parent_id] = nil

    update_result = update(id, params)

    OpenStruct.new(
      template: update_result.template,
      variables: update_result.variables,
      template_categories: update_result.template_categories
    )
  end

  def destroy_parent_template(id)
    authorize_user_roles!(@user, %w[admin partner_admin])

    template = ModelTemplate.find(id)

    # Partner admin can only delete templates in organizations they created
    if partner_admin?
      authorize! template_ownership(template), on_error: 'You are not authorized to delete this template'
    end

    destroy(id)
  end

  def add_to_bank(template_id, notes = nil)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[admin partner_admin])

    template = ModelTemplate.find_by(id: template_id)
    assert! template.present?, on_error: 'Template not found'

    assert! !template.in_bank?, on_error: 'Template is already in bank'

    authorize! template_ownership(template), on_error: 'You can only add templates from organizations you created'

    duplication_result = duplication(template, membership) do |template_attr|
      template_attr['in_bank'] = true
      template_attr['bank_added_by'] = @user.id.to_s
      template_attr['bank_added_at'] = Time.current
      template_attr['bank_notes'] = notes
      template_attr['draft'] = template['draft']
      template_attr['name'] = template['name']
      template_attr['parent_id'] = template['id']
    end

    new_template = duplication_result.template
    ratings = ModelRating.where(model_template_id: template_id).average(:rating).to_f
    new_template.update!(rating: ratings)

    OpenStruct.new(
      template: duplication_result.template,
      message: 'Template successfully added to bank'
    )
  end

  def remove_from_bank(template_id)
    authorize_user_roles!(@user, %w[admin partner_admin])

    template = ModelTemplate.find_by(id: template_id)
    assert! template.present?, on_error: 'Template not found'
    assert! template.in_bank?, on_error: 'Template is not in bank'

    authorize! template_ownership(template), on_error: 'You can only remove templates from organizations you created'

    template.remove_from_bank!

    OpenStruct.new(
      template: template,
      message: 'Template successfully removed from bank'
    )
  end

  def system_prompt(template_id)
    authorize! platform_admin?, on_error: 'You are not authorized to access'

    template = ModelTemplate.find(template_id)

    model = Model.find_by!(model_template_id: template.id)

    model.instruction
  end

  def duplicate(id)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin member])

    base_template = ModelTemplate.find(id)

    unless platform_admin? || (partner_admin? && template_ownership(base_template))
      return unless %w[team_admin member].include?(membership.membership_role)

      valid_team = ([base_template.organization_team_id] & membership.organization_team_ids).compact

      authorize! valid_team.present? || base_template.organization_prompt, on_error: 'Invalid team'

    end

    result = duplication(base_template, membership)

    OpenStruct.new(
      template: result.template,
      variables: result.variables,
      instruction_inputs: result.instruction_inputs,
      template_categories: result.template_categories
    )
  end

  def duplicate_to_organizations(parent_id, organization_ids)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[admin super_admin_platform owner_platform partner_admin])

    if partner_admin?
      parent_template = ModelTemplate.find(parent_id)
      authorize! template_ownership(parent_template),
                 on_error: 'You are not authorized to duplicate templates to this organization'

      authorize! check_allowed_org_ids!(organization_ids),
                 on_error: 'You are not authorized to duplicate templates to this organization'
    end

    parent_template = ModelTemplate.find(parent_id)
    duplicated_results = []

    ActiveRecord::Base.transaction do
      organization_ids.each do |org_id|
        organization = Organization.find(org_id)

        # Use duplication method with skip_transaction to avoid nested transactions
        duplication_result = duplication(parent_template, membership) do |template_attr|
          template_attr['organization_id'] = organization.id
          template_attr['parent_id'] = parent_template.id
          # Remove the " - Copy" suffix since this is a child template
          template_attr['name'] = template_attr['name'].gsub(' - Copy', '')
          template_attr['in_bank'] = false
          template_attr['bank_added_by'] = nil
          template_attr['bank_added_at'] = nil
          template_attr['bank_notes'] = nil
        end

        duplicated_results << duplication_result

        # Create models for all available model types
        create_models_for_template(duplication_result.template)
      end

      # Update parent's child count
      parent_template.update_child_count!
    end

    OpenStruct.new(
      parent_template: parent_template,
      duplicated_templates: duplicated_results.map(&:template),
      duplicated_results: duplicated_results
    )
  end

  def duplicate_parent_template(id)
    authorize_user_roles!(@user, %w[admin super_admin_platform owner_platform partner_admin])

    parent_template = ModelTemplate.find(id)

    if partner_admin?
      authorize! template_ownership(parent_template), on_error: 'You are not authorized to duplicate this template'
    end

    # Create a new parent template
    new_template_attr = parent_template.dup.attributes.compact.slice(
      'name', 'description', 'max_tokens', 'temperature', 'model', 'instruction', 'prompt',
      'placeholder', 'verified', 'organization_prompt', 'draft', 'reference_output_url',
      'organization_id', 'template_category_id', 'organization_team_id'
    )
    new_template_attr['user_id'] = @user.id
    new_template_attr['name'] = new_template_attr['name'] + ' - Copy'
    new_template_attr['parent_id'] = nil

    duplicated_template = nil

    ActiveRecord::Base.transaction do
      duplicated_template = ModelTemplate.create!(new_template_attr)

      # Duplicate variables
      duplicate_template_variables(parent_template, duplicated_template)

      # Duplicate instruction inputs
      duplicate_template_ins(parent_template, duplicated_template)

      # Create models for all available model types
      create_models_for_template(duplicated_template)
    end

    OpenStruct.new(
      duplicated_template: duplicated_template,
      variables: duplicated_template.model_template_variables,
      template_categories: TemplateCategory.where(id: duplicated_template.template_category_id)
    )
  end

  def build_system_prompt(model_template)
    system_prompt_origin = <<~PROMPT
      This agent is used to help users generate outputs for the use case of
      <agent_caption_context>

      This agent must draw closely from the reference output to create an output that closely resemble this original output.

      Example Reference Output: "<reference_output_context>"

      Aside from that, there are a set of key variables and inputs that need to be considered in descending order of priority. These are the high, medium and low priority inputs and variables. High priority inputs and variables are given the heaviest 'weight' in consideration of generation of these outputs, followed by medium and low respectively.

      Both variables and inputs of the came category carry an equal amount of weight.

      Of highest priority in terms of consideration:

      High Priority Inputs:
      <high_priority_inputs>

      High Priority Variables:
      <high_priority_variables>

      Of medium priority in terms of consideration:
      Medium Priority Inputs:
      <medium_priority_inputs>

      Medium Priority Variables:
      <medium_priority_variables>

      Of low priority in terms of consideration:
      Low Priority Inputs:
      <low_priority_inputs>

      Low Priority Variables:
      <low_priority_variables>

      Additionally, all agents must consider the following relationships and rules between the variables, inputs and reference output. These are the following inputs and variables which will be considered in the rules, as indicated above.
      <list_input_names>

      <list_input_variables>

      Rules: "<rules_context>"
    PROMPT

    agent_caption_context = model_template.description || ''
    reference_output_context = model_template.placeholder || ''
    rules = model_template.prompt || ''

    model_template_inputs = ModelTemplateIn.where(model_template_id: model_template.id)
                                           .pluck(:name, :description, :weight, :id)
    model_template_variables = ModelTemplateVariable.where(model_template_id: model_template.id)
                                                    .pluck(:name, :description, :weight, :id)

    # object structure [name, description, weight, id]
    arrange_priority = lambda do |objects, priority|
      objects.filter { |i| i[2] == priority }.map do |i|
        <<~VARIABLES
          #{i[0]}: #{i[1]}

        VARIABLES
      end.join
    end

    high_priority_inputs = arrange_priority.call(model_template_inputs, 'high')
    high_priority_variables = arrange_priority.call(model_template_variables, 'high')

    medium_priority_inputs = arrange_priority.call(model_template_inputs, 'medium')
    medium_priority_variables = arrange_priority.call(model_template_variables, 'medium')

    low_priority_inputs = arrange_priority.call(model_template_inputs, 'low')
    low_priority_variables = arrange_priority.call(model_template_variables, 'low')

    list_input_names = model_template_inputs.map do |i|
      <<~VARIABLES
        #{i[0]}: #{i[1]}

      VARIABLES
    end.join

    list_input_variables = model_template_variables.map do |i|
      <<~VARIABLES
        #{i[0]}: #{i[1]}

      VARIABLES
    end.join

    tagged_on_rules = {}
    model_template_inputs.each do |data|
      tagged_on_rules["<@I#{data[3]}>"] = data[0]
    end

    model_template_variables.each do |data|
      tagged_on_rules["<@V#{data[3]}>"] = data[0]
    end

    mapped_rules = rules.gsub(/<@[IV]\d+>/, tagged_on_rules)
    mapped_rules = mapped_rules.gsub('<@RefOutput>', 'Reference Output')

    system_prompt_origin.gsub('<agent_caption_context>', agent_caption_context)
                        .gsub('<reference_output_context>', reference_output_context)
                        .gsub('<rules_context>', mapped_rules)
                        .gsub('<high_priority_inputs>', high_priority_inputs)
                        .gsub('<high_priority_variables>', high_priority_variables)
                        .gsub('<medium_priority_inputs>', medium_priority_inputs)
                        .gsub('<medium_priority_variables>', medium_priority_variables)
                        .gsub('<low_priority_inputs>', low_priority_inputs)
                        .gsub('<low_priority_variables>', low_priority_variables)
                        .gsub('<list_input_names>', list_input_names)
                        .gsub('<list_input_variables>', list_input_variables)
  end

  def assign_parent_template(id, params)
    authorize_user_roles!(@user, [])

    template = ModelTemplate.find(id)
    assigned_to_org = Organization.find(params[:organization_id])
    # check if any partner_admin on org or has org as primary managed organization
    membership = Membership.where(role: Membership.role_mappings['partner_admin'])
                           .where('organization_id = ? OR managed_organization_ids[1] = ?',
                                  assigned_to_org.id, assigned_to_org.id).first
    authorize! membership.present?, on_error: 'No partner admin found on organization'

    PartnerAssignedModelTemplate.import [
      {
        model_template_id: template.id,
        organization_id: assigned_to_org.id,
        discarded_at: nil
      }
    ], on_duplicate_key_update: [:discarded_at]

    OpenStruct.new(
      template: template,
      assigned_to_org: assigned_to_org,
      variables: template.model_template_variables,
      template_categories: TemplateCategory.where(id: template.template_category_id)
    )
  end

  private

  def duplication(base_template, membership)
    ModelTemplate.new
    new_variables = []
    new_inputs = []
    template_categories = [base_template.template_category].compact

    # Duplicate template
    template_attr = base_template.dup.attributes.compact.slice(
      'name', 'description', 'max_tokens', 'temperature', 'model', 'instruction', 'prompt',
      'placeholder', 'verified', 'organization_prompt', 'draft', 'reference_output_url',
      'organization_id', 'template_category_id', 'organization_team_id'
    )

    template_attr['user_id'] = @user.id
    template_attr['name'] = template_attr['name'] + ' - Copy'
    template_attr['parent_id'] = base_template.id

    # Set as personal template when member role duplicating org prompt
    if membership.membership_role == 'member' && base_template.organization_prompt
      template_attr['organization_prompt'] = false
      template_attr['organization_team_id'] = nil
    end

    yield(template_attr) if block_given?

    create_template_result = create(template_attr.with_indifferent_access)
    new_template = create_template_result.template

    new_rules = new_template.prompt

    # Duplicate variables
    variable_service = ModelTemplateVariableService.new(@user)
    base_variables = ModelTemplateVariable.where(model_template_id: base_template.id)
    base_variables.each do |v|
      new_var_attr = v.dup.attributes.compact.slice(
        'name', 'description', 'weight', 'variable_reference_url', 'order'
      )
      new_var_attr['model_template_id'] = new_template.id

      new_var = variable_service.create(new_var_attr.with_indifferent_access)
      new_variables << new_var

      new_rules = new_rules.gsub("<@V#{v.id}>", "<@V#{new_var.id}>")
    end

    # Duplicate inputs
    input_service = ModelTemplateInService.new(@user)
    base_inputs = ModelTemplateIn.where(model_template_id: base_template.id)
    base_inputs.each do |i|
      new_in_attr = i.dup.attributes.compact.slice(
        'name', 'description', 'input_reference_url', 'order'
      )
      new_in_attr['model_template_id'] = new_template.id

      new_in = input_service.create(new_in_attr.with_indifferent_access)
      new_inputs << new_in

      new_rules = new_rules.gsub("<@I#{i.id}>", "<@I#{new_in.id}>")
    end

    update(new_template.id, { prompt: new_rules }.with_indifferent_access)

    new_template.reload

    OpenStruct.new(
      template: new_template,
      variables: new_variables,
      instruction_inputs: new_inputs,
      template_categories: template_categories
    )
  end

  def duplicate_template_variables(parent_template, child_template)
    parent_variables = ModelTemplateVariable.where(model_template_id: parent_template.id)
    parent_variables.each do |variable|
      new_var_attr = variable.dup.attributes.compact.slice(
        'name', 'description', 'weight', 'variable_reference_url', 'order'
      )
      new_var_attr['model_template_id'] = child_template.id

      ModelTemplateVariable.create!(new_var_attr)
    end
  end

  def duplicate_template_ins(parent_template, child_template)
    parent_ins = ModelTemplateIn.where(model_template_id: parent_template.id)
    parent_ins.each do |input|
      new_in_attr = input.dup.attributes.compact.slice(
        'name', 'description', 'input_reference_url', 'order'
      )
      new_in_attr['model_template_id'] = child_template.id

      ModelTemplateIn.create!(new_in_attr)
    end
  end

  def create_models_for_template(template)
    result_rules = build_system_prompt(template)

    Model.models.values.each do |m|
      Model.create!(
        organization_id: template.organization_id,
        name: "#{template.name} #{m}",
        model: m,
        instruction: result_rules,
        temperature: template.temperature,
        max_tokens: template.max_tokens,
        model_template_id: template.id
      )
    end
  end

  def update_models_for_template(template)
    result_rules = build_system_prompt(template)
    list_models = Model.models.values
    models = Model.where(model_template_id: template.id)

    # Update existing models
    models.each do |model|
      model.update!(
        name: "#{template.name} #{model.model}",
        instruction: result_rules,
        temperature: template.temperature,
        max_tokens: template.max_tokens
      )
    end

    # Create models for missing model types
    not_created_models = list_models - models.pluck(:model).uniq.compact
    return if not_created_models.empty?

    not_created_models.each do |m|
      Model.create!(
        organization_id: template.organization_id,
        name: "#{template.name} #{m}",
        model: m,
        instruction: result_rules,
        temperature: template.temperature,
        max_tokens: template.max_tokens,
        model_template_id: template.id
      )
    end
  end

  def url_to_filename(url)
    uri = URI.parse(url)
    File.basename(uri.path)
  end

  def template_category_ownership(template_category)
    # Admin, super_admin_platform and owner_platform can access any category
    return true if platform_admin?

    @user.membership&.organization_id == template_category.organization_id
  end

  def template_ownership(template)
    # Admin, super_admin_platform and owner_platform can access any template
    return true if platform_admin?

    if partner_admin?
      user_id = @user.id.is_a?(Integer) ? @user.id.to_s : @user.id
      organization = Organization.find(template.organization_id)
      return organization.created_by_id == user_id || @user.membership.organization_id == organization.id
    end

    # Non-admin users can only access their organization's templates
    @user.membership&.organization_id == template.organization_id
  end

  def organization_team_ownership(org_team)
    # Admin, super_admin_platform and owner_platform can access any team
    return true if platform_admin?

    @user.membership&.organization_id == org_team.organization_id
  end

  def template_user_authority(template, membership)
    return true if platform_admin?

    if membership.membership_role == 'team_admin'
      # true if (either org template OR team template OR own creator) AND
      #   (template have the same team OR template's creator have the same team OR own creator)
      return  (template.organization_prompt || template.organization_team_id.present? || template.user_id == @user.id.to_s) &&
              (
                (template.organization_team_id.present? && membership.organization_team_ids.include?(template.organization_team_id)) ||
                ([template.user&.membership&.organization_team_ids].compact.flatten & membership.organization_team_ids).any? ||
                template.user_id == @user.id.to_s
              )
    elsif membership.membership_role == 'member'
      return  template.user_id == @user.id.to_s
    end

    true
  end

  def time_current
    Time.current
  end
end
