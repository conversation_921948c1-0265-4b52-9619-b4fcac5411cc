# frozen_string_literal: true

module External
  class RagService
    def initialize(organization_id)
      organization = Organization.find(organization_id)
      @scope = "#{Rails.env}-#{organization.id}"
    end

    def upload_document(params)
      input = UploadDocumentInput.new(params)

      data = JSON.dump(input.output)
      uri_path = '/api/v1/rag/upload'
      response = http.post uri_path, data

      JSON.parse(response.body)
    end

    # {
    #   "id": Integer,
    #   "status": String,
    #   "total_pages": Integer,
    #   "filename": String,
    #   "metadata": String"
    # }
    def status(document_id)
      uri_path = "/api/v1/rag/#{document_id}/status"
      response = http.get uri_path

      JSON.parse(response.body)
    end

    # {
    #   "id": Integer,
    #   "metadata": Object,
    #   "text": String,
    #   "score": Float
    # }[]
    def retrieve(query, document_ids: [], top_k: 5)
      data = {
        query:,
        partition: @scope,
        filters: {
          document_id: document_ids.map(&:to_s)
        },
        top_k:
      }

      uri_path = '/api/v1/rag/retrieve'
      response = http.post uri_path, JSON.dump(data)

      JSON.parse(response.body)
    end

    private

    def http
      @http ||= Faraday.new(options) do |faraday|
        faraday.use Faraday::Response::RaiseError
      end
    end

    def options
      {
        headers:,
        url:
      }
    end

    def headers
      {
        'RAG-API-KEY' => api_key,
        'content-type' => 'application/json',
        'accept' => 'application/json'
      }
    end

    def url
      ENV.fetch('RAG_API_URL', nil)
    end

    def api_key
      ENV.fetch('RAG_API_KEY', nil)
    end

    class UploadDocumentInput < ::ApplicationInput
      required(:url)

      def output
        output = super
        output[:file_url] = output[:url]
        output.delete(:url)

        output
      end
    end
  end
end
