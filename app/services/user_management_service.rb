# frozen_string_literal: true

class UserManagementService < AppService
  def create_invitation(params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin partner_admin])

    email = params[:email].downcase
    role = params[:role]
    organization_team_id = params[:organization_team_id]
    organization_id = membership.organization_id
    primary_workspace_id = params[:primary_workspace]
    managed_workspace_ids = params[:managed_workspaces] || []

    if membership.membership_role == 'partner_admin'
      authorize! primary_workspace_id.present? && managed_workspace_ids.present?,
                 on_error: 'Primary workspace and managed workspaces are required'
    end

    if membership.membership_role == 'super_user'
      organization_id = params.delete(:organization_id) || membership.organization_id
    end

    if %w[owner partner_admin].include?(role)
      organization_id = params.delete(:organization_id) || membership.organization_id
    end

    role_number = Membership.role_mappings[role]
    assert! role_number.present?, on_error: 'Role not found'

    authorize! role_number >= membership.role,
               on_error: 'Cannot invite with role with higher access than your role'

    user = User.find_by(email: email)

    if role == 'partner_admin' && user.present?
      err_msg = 'Primary workspace and managed workspaces are required'
      authorize! primary_workspace_id.present? && managed_workspace_ids.present?, on_error: err_msg

      managed_organization_ids = [primary_workspace_id, *managed_workspace_ids].compact.uniq

      managed_orgs = Organization.where(id: managed_organization_ids)
      exist! managed_orgs.count == managed_organization_ids.count, on_error: 'Managed organizations invalid'

      Organization.find(primary_workspace_id).update!(is_partner: true)
    end

    if user.present?
      org_membership = Membership.find_by(user_id: user.id, organization_id: organization_id)
      authorize! org_membership.blank?, on_error: 'User already in organization'

      user_invite = UserInvitation.new(email: email, organization_id: organization_id, role: role_number)

      Membership.create!(
        user_id: user.id,
        organization_id: organization_id,
        role: role_number,
        organization_team_ids: [organization_team_id],
        managed_organization_ids: managed_organization_ids
      )
      return user_invite
    end

    if organization_team_id.present?
      organization_team = OrganizationTeam.find(organization_team_id)

      err_msg = 'Organization Team Invalid'
      authorize! organization_team.organization_id == organization_id, on_error: err_msg unless platform_admin?
    end

    current_time = Time.current

    if membership.membership_role == 'super_user' && role == 'owner'
      last_invite = UserInvitation.where(
        organization_id: organization_id,
        invitation_expiry_date: current_time..,
        role: role_number,
        invitation_status: %w[invited confirmed]
      ).first

      assert! !last_invite.present?, on_error: 'User already invited'
    else
      last_invite = UserInvitation.where(
        organization_id: organization_id,
        email: email,
        invitation_expiry_date: current_time..
      ).first

      assert! !last_invite.present?, on_error: 'User already invited'
    end

    invitation_code = SecureRandom.hex(64)
    invite_with_code = UserInvitation.where(
      invitation_code: invitation_code,
      invitation_expiry_date: current_time..
    ).first

    assert! !invite_with_code.present?, on_error: 'Invite failed, please try again'

    ActiveRecord::Base.transaction do
      user_invite = UserInvitation.create!(
        email: email,
        invitation_status: 'invited',
        invitation_code: invitation_code,
        invitation_expiry_date: current_time + 7.days,
        organization_id: organization_id,
        managed_organization_ids: role == 'partner_admin' ? [primary_workspace_id, *managed_workspace_ids] : [],
        invited_by_membership_id: membership.id,
        role: role_number,
        invited_to_organization_team_id: organization_team_id
      )

      Mailer::UserInvitationMailerJob.perform_later(user_invite.id)
      Mailer::InviteMemberNotificationMailerJob.perform_later(user_invite.id)

      user_invite
    end
  end

  def accept_invitation(params)
    invitation_code = params.delete(:invite_code)
    password = params.delete(:password)
    name = params.delete(:name) || ''

    ui = UserInvitation.find_by(invitation_code: invitation_code)
    authorize! ui.present?, on_error: 'Invitation Not Found'
    authorize! ui.invitation_status != 'confirmed', on_error: 'Invitation Already Used'

    current_time = Time.current
    expired_invitation = current_time >= ui.invitation_expiry_date
    if expired_invitation
      ui.update(invitation_status: 'expired')
      assert! !expired_invitation, on_error: 'Invitation Expired'
    end

    user = User.find_by(email: ui.email)
    authorize! !user.present?, on_error: 'User already registered'

    ActiveRecord::Base.transaction do
      user = User.create!(
        email: ui.email,
        display_name: name,
        password: password
      )

      org_user = Membership.create!(
        user_id: user.id,
        organization_id: ui.organization_id,
        role: ui.role,
        invited_email: ui.email,
        code: ui.invitation_code,
        managed_organization_ids: ui.managed_organization_ids
      )

      if org_user.role == Membership.role_mappings['partner_admin']
        Organization.find(ui.organization_id).update!(is_partner: true)
      end

      ui.update!(invitation_status: 'confirmed')

      Mailer::OnboardOrganizationMailerJob.perform_later(org_user)
    end

    user
  end

  def detail_invitation(params)
    invitation_code = params[:invite_code]
    UserInvitation.find_by!(invitation_code: invitation_code)
  end

  def change_password_request(params)
    email = params[:email].downcase
    @user = User.find_by(email: email)

    assert! @user.present?, on_error: 'Email Invalid'

    membership = Membership.joins(:organization).find_by(user_id: @user.id)

    assert! membership.present?, on_error: 'You are not in any organization'

    current_time = Time.current
    last_request = TokenRequest.where(
      email: email,
      request_expiry_date: current_time..
    ).where.not(
      request_status: 'confirmed'
    ).first
    assert! !last_request.present?, on_error: 'Already requested change password'

    request_code = SecureRandom.hex(64)
    request_with_code = TokenRequest.where(
      request_code: request_code,
      request_expiry_date: current_time..
    ).first
    assert! !request_with_code.present?, on_error: 'Request failed, please try again'

    change_password_request = TokenRequest.create(
      email: email,
      request_status: 'requested',
      request_code: request_code,
      request_expiry_date: current_time + 10.minutes,
      requested_at: current_time,
      user_id: @user.id,
      purpose: 'change_password'
    )

    Mailer::PasswordChangeRequestMailerJob.perform_later(change_password_request)

    change_password_request
  end

  def change_password(params)
    request_code = params.delete(:request_code)
    password = params.delete(:password)
    password_confirmation = params.delete(:password_confirmation)

    cpr = TokenRequest.find_by(request_code: request_code)
    authorize! cpr.present?, on_error: 'Request Not Found'
    authorize! cpr.request_status != 'confirmed' && cpr.purpose == 'change_password', on_error: 'Request Invalid'

    current_time = Time.current
    expired_request = current_time >= cpr.request_expiry_date
    if expired_request
      cpr.update(request_status: 'expired')
      assert! !expired_request, on_error: 'Request Expired'
    end

    assert! password == password_confirmation, on_error: 'Wrong password confirmation'

    user = cpr.user

    ActiveRecord::Base.transaction do
      user.update(password: password)

      cpr.update(request_status: 'confirmed')
    end
  end

  def list_invited_users(params)
    verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner super_admin team_admin admin partner_admin])

    user_invitations = ::UserInvitations.new
    filter = params.slice(:organization_id, :search, :page, :per_page)
    organization_id = filter[:organization_id]

    is_member = Membership.find_by(user_id: @user.id, organization_id:)

    filter[:organization_id] = @user.membership.organization_id if is_member.blank?
    filter[:organization_id] = params[:organization_id] if params[:organization_id].present? && platform_admin?
    filter[:organization_id] ||= @user.membership.organization_id

    user_invitations.filter(filter)
  end
end
