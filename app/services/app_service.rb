# frozen_string_literal: true

class AppService
  def initialize(user = nil, current_user_organization_id = nil, selected_request_organization_id = nil)
    @user = user
    @current_user_organization_id = current_user_organization_id
    @selected_request_organization_id = selected_request_organization_id
  end

  def upload(blob, file, changed_file: false)
    blob.purge if blob.attached? && changed_file
    blob.attach(file) if file
  end

  def authorize!(*permissions, on_error: e('.not_allowed'))
    raise ExceptionHandler::Unauthorized, on_error if permissions.none?
  end

  def assert!(*truths, on_error: e('.invalid'))
    raise Invalid, on_error if truths.none?
  end

  def exist!(object, on_error: e('.none'))
    raise ExceptionHandler::NotFound, on_error if object.blank?

    object
  end

  def duplicate_attributes(record)
    record.attributes
          .with_indifferent_access
          .except(:id, :created_at, :updated_at)
  end

  def as_boolean(value, default: false)
    return default unless value.present?

    ActiveModel::Type::Boolean.new.cast(value)
  end

  def validate_email(email)
    email =~ /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i
  end

  def registered_user(email)
    User.find_by(email: email)
  end

  def authorize_user_roles!(user, roles)
    membership = Membership.where(user_id: user.id).first
    authorize! membership.present?, on_error: 'Not in any organization'

    # Allow admin, super_admin_platform and owner_platform roles to have cross-organization access
    return true if platform_admin?
    return if roles.blank?

    authorize! roles.include?(membership.membership_role), on_error: 'Not authorized'

    return unless membership.membership_role == 'super_user'

    authorize! membership.organization&.superuser_privilege, on_error: 'Not authorized'
  end

  def verify_user_organization(user)
    membership = Membership.find_by(user_id: user.id)
    organization_id = membership&.organization_id

    authorize! !organization_id.nil?, on_error: 'Not in any organization'

    membership
  end

  def platform_admin?
    platform_admins = %w[
      admin
      super_admin_platform
      owner_platform
    ]

    platform_admin_roles = platform_admins.map { |role| Membership.role_mappings[role] }

    membership = Membership.find_by(user_id: @user.id, role: platform_admin_roles)
    return false unless membership

    true
  end

  def selected_request_organization
    @selected_request_organization ||= Organization.kept.find_by(id: @selected_request_organization_id)
  end

  def current_user_organization
    @current_user_organization ||= Organization.kept.find_by(id: @current_user_organization_id)
  end

  def partner_admin?
    membership = Membership.find_by(user_id: @user.id, role: Membership.role_mappings['partner_admin'])
    return false unless membership

    true
  end

  def user_allowed_org_ids
    user_id = @user.id.is_a?(Integer) ? @user.id.to_s : @user.id
    owned_org_ids = Organization.where(created_by_id: user_id).pluck(:id)
    member_org_ids = Membership.where(user_id: user_id).pluck(:organization_id)

    (owned_org_ids + member_org_ids).uniq
  end

  def organization_ownership(organization)
    if partner_admin?
      user_id = @user.id.is_a?(Integer) ? @user.id.to_s : @user.id
      return true if organization.created_by_id == user_id
      return true if @user.membership.managed_organization_ids.include?(organization.id)

      partner_admin_org = Membership.find_by(user_id: @user.id, role: Membership.role_mappings['partner_admin'])
      return true if partner_admin_org.organization_id == organization.id

      return false
    end

    return true if @user&.role == 'super_user' || platform_admin?

    return false if organization.blank?

    organization.discarded_at.nil? && organization.id == @user&.membership&.organization_id
  end

  def check_allowed_org_ids!(organization_ids)
    return true if @user.role == 'super_user' || platform_admin?

    return unless partner_admin?

    unauthorized_org_ids = organization_ids - user_allowed_org_ids

    assert! unauthorized_org_ids.empty?,
            on_error: "You can only duplicate templates to organizations you created or your current organization. Unauthorized organization IDs: #{unauthorized_org_ids.join(', ')}"
  end

  def rakamin_service_enabler
    as_boolean(ENV.fetch('RAKAMIN_SERVICE_ENABLER', false))
  end

  private

  def t(key, options = {})
    key = "actions.#{self.class.name.underscore.tr('/', '.')}#{key}" if key.to_s.first == '.'

    I18n.translate(key, **options.reverse_merge(cascade: true))
  end

  def e(key, options = {})
    opts = options.merge(scope: [:errors].concat(Array(options[:scope])))
    t(key, opts)
  end

  class Invalid < ::StandardError
  end
end
