# frozen_string_literal: true

class PlatformRoleService < AppService
  def initialize(user, current_user_organization_id, selected_request_organization_id)
    @user = user
    @current_user_organization_id = current_user_organization_id
    @selected_request_organization_id = selected_request_organization_id

    super
  end

  def list_admin_platform_roles
    verify_user_organization(@user)
    role = Membership.find_by(user_id: @user.id).membership_role

    roles_to_include = case role
                       when 'owner_platform'
                         [Membership.role_mappings['super_admin_platform'], Membership.role_mappings['admin'],
                          Membership.role_mappings['owner_platform'], Membership.role_mappings['partner_admin']]
                       when 'super_admin_platform'
                         [Membership.role_mappings['super_admin_platform'], Membership.role_mappings['admin'],
                          Membership.role_mappings['partner_admin']]
                       when 'admin'
                         [Membership.role_mappings['admin'], Membership.role_mappings['partner_admin']]
                       else
                         []
                       end

    authorize! roles_to_include.any?, on_error: 'Not authorized'

    # Get activated memberships
    memberships = Membership.includes(:user, :organization)
                            .where(role: roles_to_include)
                            .order('users.email')

    # Get pending invitations for the same roles
    pending_invitations = UserInvitation.includes(:organization)
                                        .where(role: roles_to_include, invitation_status: 'invited')
                                        .order(:email)

    # Combine both into a single list for the output
    all_platform_roles = memberships.to_a + pending_invitations.to_a

    OpenStruct.new(
      memberships: all_platform_roles
    )
  end
end
