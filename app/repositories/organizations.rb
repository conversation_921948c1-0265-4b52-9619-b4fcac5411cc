# frozen_string_literal: true

class Organizations < ApplicationRepository
  def default_scope
    ::Organization.all
  end

  def filter_by_search(search)
    @scope.where('name ilike ?', "%#{search}%")
  end

  def filter_by_created_by_id(created_by_id)
    @scope.where(created_by_id: created_by_id)
  end

  def filter_by_organization_id(organization_id)
    @scope.where(id: organization_id)
  end

  def filter_by_is_partner(is_partner)
    @scope.where(is_partner: is_partner)
  end
end
